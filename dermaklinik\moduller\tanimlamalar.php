<?php
// Önce definitions tablosunun var olup olmadığını kontrol et
try {
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'definitions'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        // Tablo yoksa oluştur
        $pdo->exec("CREATE TABLE definitions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            value VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
    }
} catch (Exception $e) {
    // Tablo oluşturma hatası
}

// Tanımlamaları çek
try {
    $stmt = $pdo->prepare("SELECT * FROM definitions ORDER BY type, value");
    $stmt->execute();
    $definitions = $stmt->fetchAll();
    
    // Debug için konsola yazdır
    echo "<script>console.log('Tanımlar yüklendi:', " . json_encode($definitions) . ");</script>";
} catch (Exception $e) {
    $definitions = [];
    echo "<script>console.log('Tanım yükleme hatası:', " . json_encode($e->getMessage()) . ");</script>";
}

// Tip gruplarına ayır
$grouped_definitions = [];
foreach ($definitions as $def) {
    $grouped_definitions[$def['type']][] = $def;
}

// Mevcut tipler
$types = [
    'islem_tipi' => 'İşlem Tipleri',
    'bolge' => 'Bölgeler',
    'urun' => 'Ürünler',
    'personel' => 'Personel',
    'cihaz' => 'Cihazlar',
    'odeme_sekli' => 'Ödeme Şekilleri'
];
?>

<h2>Tanımlamalar</h2>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
    <?php foreach ($types as $type_key => $type_name): ?>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50; display: flex; justify-content: space-between; align-items: center;">
            <?= $type_name ?>
            <button class="btn-primary" onclick="openAddDefinitionModal('<?= $type_key ?>')" style="padding: 5px 10px; font-size: 0.8rem;">+ Ekle</button>
        </h3>
        
        <div id="definitions-<?= $type_key ?>">
            <?php if (isset($grouped_definitions[$type_key])): ?>
                <?php foreach ($grouped_definitions[$type_key] as $def): ?>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #eee;">
                    <span><?= htmlspecialchars($def['value']) ?></span>
                    <div>
                        <button class="btn-info" onclick="editDefinition(<?= $def['id'] ?>)" style="padding: 3px 8px; font-size: 0.7rem; margin-right: 5px;">Düzenle</button>
                        <button class="btn-danger" onclick="deleteDefinition(<?= $def['id'] ?>)" style="padding: 3px 8px; font-size: 0.7rem;">Sil</button>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p style="color: #7f8c8d; text-align: center; padding: 20px;">Henüz tanım eklenmemiş.</p>
            <?php endif; ?>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Tanım Ekleme Modal -->
<div id="addDefinitionModal" class="modal">
    <div class="modal-content" style="max-width:400px;">
        <div class="modal-header">
            <h2>Yeni Tanım Ekle</h2>
            <span class="close-btn" onclick="closeModal('addDefinitionModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="addDefinitionForm">
                <input type="hidden" id="definitionType" name="type">
                <div class="form-group">
                    <label>Tip</label>
                    <input type="text" id="definitionTypeDisplay" disabled style="background:#f5f5f5;">
                </div>
                <div class="form-group">
                    <label>Değer</label>
                    <input type="text" name="value" required>
                </div>
                <button type="submit" class="btn-primary">Kaydet</button>
                <button type="button" onclick="closeModal('addDefinitionModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Tanım Düzenleme Modal -->
<div id="editDefinitionModal" class="modal">
    <div class="modal-content" style="max-width:400px;">
        <div class="modal-header">
            <h2>Tanım Düzenle</h2>
            <span class="close-btn" onclick="closeModal('editDefinitionModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="editDefinitionForm">
                <input type="hidden" id="editDefinitionId" name="id">
                <div class="form-group">
                    <label>Tip</label>
                    <input type="text" id="editDefinitionType" disabled style="background:#f5f5f5;">
                </div>
                <div class="form-group">
                    <label>Değer</label>
                    <input type="text" id="editDefinitionValue" name="value" required>
                </div>
                <button type="submit" class="btn-primary">Güncelle</button>
                <button type="button" onclick="closeModal('editDefinitionModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<script>
function openAddDefinitionModal(type) {
    const typeNames = {
        'islem_tipi': 'İşlem Tipleri',
        'bolge': 'Bölgeler',
        'urun': 'Ürünler',
        'personel': 'Personel',
        'cihaz': 'Cihazlar',
        'odeme_sekli': 'Ödeme Şekilleri'
    };
    
    document.getElementById('definitionType').value = type;
    document.getElementById('definitionTypeDisplay').value = typeNames[type];
    openModal('addDefinitionModal');
}

function editDefinition(id) {
    // Bu fonksiyon tanım düzenleme için kullanılacak
    Swal.fire({
        icon: 'info',
        title: 'Bilgi',
        text: 'Tanım düzenleme özelliği yakında eklenecek. ID: ' + id,
        confirmButtonText: 'Tamam'
    });
}

function deleteDefinition(id) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu tanımı silmek istediğinizden emin misiniz?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
    
            fetch('actions/tanim_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=delete_definition&id=' + id
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: 'Bir hata oluştu!',
                    confirmButtonText: 'Tamam'
                });
            });
        }
    });
}

// Tanım ekleme formu
document.getElementById('addDefinitionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('action', 'add_definition');
    
    console.log('Form gönderiliyor:', {
        type: formData.get('type'),
        value: formData.get('value')
    });
    
    fetch('actions/tanim_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text(); // Önce text olarak al
    })
    .then(text => {
        console.log('Response text:', text);
        try {
            const data = JSON.parse(text);
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı!',
                    text: data.message,
                    confirmButtonText: 'Tamam'
                            }).then(() => {
                closeModal('addDefinitionModal');
                // Yeni tanımı DOM'a ekle
                addDefinitionToDOM(formData.get('type'), formData.get('value'));
            });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: data.message,
                    confirmButtonText: 'Tamam'
                });
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: 'Sunucu yanıtı işlenemedi: ' + text,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu: ' + error.message,
            confirmButtonText: 'Tamam'
        });
    });
});

// Yeni tanımı DOM'a ekle
function addDefinitionToDOM(type, value) {
    const container = document.getElementById('definitions-' + type);
    if (!container) return;
    
    // Eğer "Henüz tanım eklenmemiş" mesajı varsa kaldır
    const emptyMsg = container.querySelector('p');
    if (emptyMsg) {
        emptyMsg.remove();
    }
    
    // Yeni tanım div'i oluştur
    const newDefDiv = document.createElement('div');
    newDefDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #eee;';
    newDefDiv.innerHTML = `
        <span>${value}</span>
        <div>
            <button class="btn-info" onclick="editDefinition(0)" style="padding: 3px 8px; font-size: 0.7rem; margin-right: 5px;">Düzenle</button>
            <button class="btn-danger" onclick="deleteDefinition(0)" style="padding: 3px 8px; font-size: 0.7rem;">Sil</button>
        </div>
    `;
    
    // Container'a ekle
    container.appendChild(newDefDiv);
    
    console.log('Yeni tanım eklendi:', type, value);
}
</script>
