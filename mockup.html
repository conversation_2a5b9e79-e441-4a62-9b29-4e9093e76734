<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Ta<PERSON>p Sistemi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        nav ul {
            display: flex;
            list-style: none;
        }
        nav ul li {
            margin-left: 20px;
        }
        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        nav ul li a:hover {
            background: #34495e;
        }
        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-top: 20px;
        }
        .active {
            display: block;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.95rem;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        button {
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }
        button:hover {
            background: #2980b9;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #d68910;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.95rem;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
        }

        /* MODAL STYLES */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            overflow: auto;
            padding: 20px 0;
        }
        .modal-content {
            background: white;
            margin: 30px auto;
            max-width: 95%;
            max-width: 1000px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
        }
        .modal-header h2 {
            margin: 0;
            font-weight: 700;
            font-size: 1.8rem;
        }
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            cursor: pointer;
            font-size: 28px;
            font-weight: bold;
            color: white;
        }
        .modal-body {
            padding: 30px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            font-size: 0.95rem;
            line-height: 1.6;
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #eef2ff;
            margin-bottom: 30px;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: 600;
            color: #2c3e50;
        }
        .section-title {
            color: #2c3e50;
            font-weight: 600;
            margin: 20px 0 15px;
            padding-left: 12px;
            border-left: 4px solid #3498db;
            display: flex;
            align-items: center;
        }
        .section-title.laser {
            border-left-color: #9b59b6;
        }
        .section-title.payments {
            border-left-color: #e67e22;
        }
        .date-group {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
        }
        .date-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }
        .date-group-title {
            font-weight: 600;
            font-size: 1.1rem;
            color: #1e293b;
        }
        .date-group-summary {
            background: #f1f5f9;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        .payment-btn {
            background: #e67e22;
            padding: 4px 10px;
            font-size: 0.85rem;
        }
        .payment-btn:hover {
            background: #d35400;
        }
        .paid-badge {
            background: #27ae60;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-warning {
            background: #f39c12;
            color: white;
        }
        .badge-success {
            background: #27ae60;
            color: white;
        }

        @media (max-width: 768px) {
            nav ul {
                flex-direction: column;
            }
            nav ul li {
                margin: 5px 0;
            }
            th, td {
                padding: 8px 4px;
                font-size: 14px;
            }
            .modal-content {
                margin: 10px;
                max-width: 95%;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <h2>Hasta Takip Sistemi</h2>
        <nav>
            <ul>
                <li><a href="#" onclick="showTab('dashboard')">Dashboard</a></li>
                <li><a href="#" onclick="showTab('patients')">Hastalar</a></li>
                <li><a href="#" onclick="showTab('appointments')">Randevular</a></li>
                <li><a href="#" onclick="showTab('reports')">Raporlar</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">

        <!-- Dashboard -->
        <div id="dashboard" class="tab-content active">
            <h3>Dashboard</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #3498db; color: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h4>Günlük Randevular</h4>
                    <h2 id="daily-appointments">0</h2>
                </div>
                <div style="background: #2ecc71; color: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h4>Toplam Gelir (Bugün)</h4>
                    <h2 id="daily-income">0 TL</h2>
                </div>
                <div style="background: #e67e22; color: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h4>Toplam Hasta</h4>
                    <h2 id="total-patients">0</h2>
                </div>
            </div>
            <button onclick="showTab('patients')">Hasta Ekle</button>
        </div>

        <!-- Hastalar -->
        <div id="patients" class="tab-content">
            <h3>Hastalar</h3>
            <div class="form-group" style="max-width: 400px;">
                <input type="text" id="search-patient" placeholder="Ad, TC veya Telefon ile ara..." oninput="filterPatients()" style="padding: 12px; font-size: 1rem;">
            </div>
            <button onclick="openPatientModal()">+ Yeni Hasta Ekle</button>
            <table id="patients-table">
                <thead>
                    <tr>
                        <th>Ad Soyad</th>
                        <th>TC</th>
                        <th>Telefon</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody id="patients-list">
                    <!-- Dinamik olarak doldurulacak -->
                </tbody>
            </table>
        </div>

        <!-- Hasta Detay (Modern Modal) -->
        <div id="patient-detail-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">Hasta Detay</h2>
                    <span class="close-btn" onclick="closePatientModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="info-grid" id="patient-info"></div>

                    <!-- Genel İşlemler -->
                    <h3 class="section-title">💉 Genel İşlemler</h3>
                    <button onclick="openSessionModal()" style="background:#3498db; margin-bottom:15px;">+ Yeni İşlem Ekle</button>
                    <div id="sessions-grouped"></div>

                    <!-- Lazer İşlemleri -->
                    <h3 class="section-title laser">🔬 Lazer İşlemleri</h3>
                    <button onclick="openLaserModal()" style="background:#9b59b6; margin-bottom:15px;">+ Yeni Lazer İşlemi</button>
                    <div id="laser-grouped"></div>

                    <!-- Tahsilatlar -->
                    <h3 class="section-title payments">💰 Tahsilat Geçmişi</h3>
                    <div style="background:#fffaf0; padding:15px; border-radius:10px; border:1px solid #fadfad;">
                        <table>
                            <thead>
                                <tr style="background:#e67e22; color:white;">
                                    <th>Tarih</th>
                                    <th>Tutar</th>
                                    <th>Ödeme Şekli</th>
                                    <th>Açıklama</th>
                                </tr>
                            </thead>
                            <tbody id="collections-list"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Randevular -->
        <div id="appointments" class="tab-content">
            <h3>Randevular</h3>
            <button onclick="openAppointmentModal()">+ Yeni Randevu</button>
            <table>
                <thead>
                    <tr>
                        <th>Hasta</th>
                        <th>Tarih</th>
                        <th>Saat</th>
                        <th>İşlem</th>
                        <th>Durum</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody id="appointments-list"></tbody>
            </table>
        </div>

        <!-- Raporlar -->
        <div id="reports" class="tab-content">
            <h3>Raporlar</h3>
            <div class="form-group" style="max-width: 300px;">
                <label>Rapor Türü</label>
                <select id="report-type" style="padding:10px;">
                    <option value="gelir">Gelir Raporu</option>
                    <option value="hasta">Hasta İşlem Raporu</option>
                    <option value="borc">Borç Raporu</option>
                </select>
            </div>
            <button onclick="generateReport()" style="padding:10px 20px; font-size:1rem;">Rapor Oluştur</button>
            <div id="report-output" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; min-height: 100px;"></div>
        </div>

    </div>

    <!-- MODALS -->
    <!-- Hasta Ekle Modal -->
    <div id="patient-modal" class="modal">
        <div class="modal-content" style="max-width:500px;">
            <div class="modal-header">
                <h2>Yeni Hasta Ekle</h2>
                <span class="close-btn" onclick="closeModal('patient-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Ad Soyad</label>
                    <input type="text" id="patient-name" required>
                </div>
                <div class="form-group">
                    <label>TC Kimlik No</label>
                    <input type="text" id="patient-tc" required>
                </div>
                <div class="form-group">
                    <label>Doğum Tarihi</label>
                    <input type="date" id="patient-dob">
                </div>
                <div class="form-group">
                    <label>Cinsiyet</label>
                    <select id="patient-gender">
                        <option value="Erkek">Erkek</option>
                        <option value="Kadın">Kadın</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Telefon</label>
                    <input type="text" id="patient-phone">
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" id="patient-email">
                </div>
                <div class="form-group">
                    <label>Adres</label>
                    <textarea id="patient-address" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>Deri Tipi</label>
                    <input type="text" id="patient-skin">
                </div>
                <div class="form-group">
                    <label>Ek Hastalıklar</label>
                    <textarea id="patient-diseases" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>Kullandığı İlaçlar</label>
                    <textarea id="patient-meds" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>Notlar</label>
                    <textarea id="patient-notes" rows="2"></textarea>
                </div>
                <button onclick="savePatient()" style="padding:10px 20px; font-size:1rem;">Kaydet</button>
                <button onclick="closeModal('patient-modal')" class="btn-danger" style="padding:10px 20px; font-size:1rem;">İptal</button>
            </div>
        </div>
    </div>

    <!-- Genel İşlem Ekle Modal -->
    <div id="session-modal" class="modal">
        <div class="modal-content" style="max-width:500px;">
            <div class="modal-header">
                <h2>Yeni İşlem Ekle</h2>
                <span class="close-btn" onclick="closeModal('session-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="current-patient-id">
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" id="session-date">
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" id="session-type" placeholder="Aquapell, PRP vb.">
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" id="session-area">
                </div>
                <div class="form-group">
                    <label>Kullanılan Ürün</label>
                    <input type="text" id="session-product">
                </div>
                <div class="form-group">
                    <label>Ücret (TL)</label>
                    <input type="number" id="session-fee" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label>Uygulayan Personel</label>
                    <input type="text" id="session-staff">
                </div>
                <div class="form-group">
                    <label>Açıklama</label>
                    <textarea id="session-desc" rows="2"></textarea>
                </div>
                <button onclick="saveSession()" style="padding:10px 20px; font-size:1rem;">Kaydet</button>
                <button onclick="closeModal('session-modal')" class="btn-danger" style="padding:10px 20px; font-size:1rem;">İptal</button>
            </div>
        </div>
    </div>

    <!-- Lazer İşlemi Modal -->
    <div id="laser-modal" class="modal">
        <div class="modal-content" style="max-width:600px;">
            <div class="modal-header">
                <h2>Yeni Lazer İşlemi</h2>
                <span class="close-btn" onclick="closeModal('laser-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="laser-patient-id">
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" id="laser-date">
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" id="laser-area" placeholder="Yüz, Bacak, Kol">
                </div>
                <div class="form-group">
                    <label>Cihaz</label>
                    <input type="text" id="laser-device" placeholder="GentleLase vb.">
                </div>
                <div class="form-group">
                    <label>Joule</label>
                    <input type="text" id="laser-joule">
                </div>
                <div class="form-group">
                    <label>% Oranı</label>
                    <input type="number" id="laser-percent" min="0" max="100" placeholder="Opsiyonel">
                </div>
                <div class="form-group">
                    <label>Spot</label>
                    <input type="text" id="laser-spot">
                </div>
                <div class="form-group">
                    <label>Time</label>
                    <input type="text" id="laser-time">
                </div>
                <div class="form-group">
                    <label>Başlık</label>
                    <input type="text" id="laser-title">
                </div>
                <div class="form-group">
                    <label>Ücret (TL)</label>
                    <input type="number" id="laser-fee" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label>Uygulayan Personel</label>
                    <input type="text" id="laser-staff">
                </div>
                <button onclick="saveLaserSession()" style="padding:10px 20px; font-size:1rem;">Kaydet</button>
                <button onclick="closeModal('laser-modal')" class="btn-danger" style="padding:10px 20px; font-size:1rem;">İptal</button>
            </div>
        </div>
    </div>

    <!-- Randevu Ekle Modal -->
    <div id="appointment-modal" class="modal">
        <div class="modal-content" style="max-width:500px;">
            <div class="modal-header">
                <h2>Yeni Randevu</h2>
                <span class="close-btn" onclick="closeModal('appointment-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Hasta</label>
                    <select id="appointment-patient">
                        <option value="">Seçiniz</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" id="appointment-date">
                </div>
                <div class="form-group">
                    <label>Saat</label>
                    <input type="time" id="appointment-time">
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" id="appointment-procedure">
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" id="appointment-area">
                </div>
                <div class="form-group">
                    <label>Durum</label>
                    <select id="appointment-status">
                        <option value="Bekliyor">Bekliyor</option>
                        <option value="Tamamlandı">Tamamlandı</option>
                        <option value="İptal">İptal</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Notlar</label>
                    <textarea id="appointment-notes" rows="2"></textarea>
                </div>
                <button onclick="saveAppointment()" style="padding:10px 20px; font-size:1rem;">Kaydet</button>
                <button onclick="closeModal('appointment-modal')" class="btn-danger" style="padding:10px 20px; font-size:1rem;">İptal</button>
            </div>
        </div>
    </div>

    <!-- ÖDEME YAP Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content" style="max-width:450px;">
            <div class="modal-header" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);">
                <h2>💰 Ödeme Yap</h2>
                <span class="close-btn" onclick="closeModal('payment-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="payment-session-id">
                <input type="hidden" id="payment-session-type"> <!-- 'genel' veya 'lazer' -->

                <div class="form-group">
                    <label>İşlem Tarihi</label>
                    <input type="text" id="payment-session-date" disabled style="background:#f5f5f5; color:#2c3e50; font-weight:bold;">
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" id="payment-session-type-display" disabled style="background:#f5f5f5; color:#2c3e50; font-weight:bold;">
                </div>
                <div class="form-group">
                    <label>Toplam Tutar (TL)</label>
                    <input type="number" id="payment-total" disabled style="background:#f5f5f5; font-weight:bold; color:#e74c3c;">
                </div>
                <div class="form-group">
                    <label>Ödenen Tutar (TL)</label>
                    <input type="number" id="payment-paid" disabled style="background:#f5f5f5; font-weight:bold; color:#27ae60;">
                </div>
                <div class="form-group">
                    <label>Ödenecek Tutar (TL)</label>
                    <input type="number" id="payment-amount" min="0" step="0.01" placeholder="Ödenecek miktar">
                </div>
                <div class="form-group">
                    <label>Ödeme Şekli</label>
                    <select id="payment-method">
                        <option value="Nakit">Nakit</option>
                        <option value="Kredi Kartı">Kredi Kartı</option>
                        <option value="Havale">Havale</option>
                        <option value="Taksit">Taksit</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Açıklama</label>
                    <input type="text" id="payment-description" placeholder="Ödeme açıklaması (opsiyonel)">
                </div>
                <button onclick="savePayment()" style="background:#27ae60; padding:10px 20px; font-size:1rem; width:100%;">✅ Ödemeyi Kaydet</button>
            </div>
        </div>
    </div>

    <script>
        // GLOBAL STATE
        let currentPatientId = null;

        // INIT
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            loadPatients();
            loadAppointments();
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('session-date').value = today;
            document.getElementById('laser-date').value = today;
            document.getElementById('appointment-date').value = today;
        });

        // TAB SWITCHING
        function showTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabId).classList.add('active');
            if (tabId === 'dashboard') loadDashboard();
            if (tabId === 'patients') loadPatients();
            if (tabId === 'appointments') loadAppointments();
        }

        // MODAL CONTROLS
        function openPatientModal() {
            document.getElementById('patient-modal').style.display = 'block';
        }
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        function openSessionModal() {
            if (!currentPatientId) return alert('Hasta seçili değil!');
            document.getElementById('current-patient-id').value = currentPatientId;
            document.getElementById('session-modal').style.display = 'block';
        }
        function openLaserModal() {
            if (!currentPatientId) return alert('Hasta seçili değil!');
            document.getElementById('laser-patient-id').value = currentPatientId;
            document.getElementById('laser-modal').style.display = 'block';
        }
        function openAppointmentModal() {
            populatePatientSelect();
            document.getElementById('appointment-modal').style.display = 'block';
        }
        function closePatientModal() {
            document.getElementById('patient-detail-modal').style.display = 'none';
        }

        // HASTA İŞLEMLERİ
        function loadPatients() {
            const patients = JSON.parse(localStorage.getItem('patients') || '[]');
            const tableBody = document.getElementById('patients-list');
            tableBody.innerHTML = '';
            patients.forEach(patient => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${patient.ad_soyad}</td>
                    <td>${patient.tc_kimlik_no}</td>
                    <td>${patient.telefon}</td>
                    <td>
                        <button onclick="viewPatient(${patient.id})">Detay</button>
                        <button onclick="deletePatient(${patient.id})" class="btn-danger">Sil</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
            document.getElementById('total-patients').innerText = patients.length;
        }

        function savePatient() {
            const patients = JSON.parse(localStorage.getItem('patients') || '[]');
            const newPatient = {
                id: Date.now(),
                ad_soyad: document.getElementById('patient-name').value,
                tc_kimlik_no: document.getElementById('patient-tc').value,
                dogum_tarihi: document.getElementById('patient-dob').value,
                cinsiyet: document.getElementById('patient-gender').value,
                telefon: document.getElementById('patient-phone').value,
                email: document.getElementById('patient-email').value,
                adres: document.getElementById('patient-address').value,
                deri_tipi: document.getElementById('patient-skin').value,
                ek_hastaliklar: document.getElementById('patient-diseases').value,
                kullandigi_ilaclar: document.getElementById('patient-meds').value,
                notlar: document.getElementById('patient-notes').value,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            patients.push(newPatient);
            localStorage.setItem('patients', JSON.stringify(patients));
            closeModal('patient-modal');
            loadPatients();
            alert('Hasta kaydedildi!');
        }

        function viewPatient(id) {
            currentPatientId = id;
            const patients = JSON.parse(localStorage.getItem('patients') || '[]');
            const patient = patients.find(p => p.id === id);
            if (!patient) return;

            document.getElementById('modal-title').innerText = `Hasta: ${patient.ad_soyad}`;
            const infoDiv = document.getElementById('patient-info');
            infoDiv.innerHTML = `
                <div class="info-item"><span class="info-label">Ad Soyad:</span> ${patient.ad_soyad}</div>
                <div class="info-item"><span class="info-label">TC:</span> ${patient.tc_kimlik_no}</div>
                <div class="info-item"><span class="info-label">Telefon:</span> ${patient.telefon}</div>
                <div class="info-item"><span class="info-label">Doğum Tarihi:</span> ${formatDate(patient.dogum_tarihi)}</div>
                <div class="info-item"><span class="info-label">Cinsiyet:</span> ${patient.cinsiyet}</div>
                <div class="info-item"><span class="info-label">Email:</span> ${patient.email || '-'}</div>
                <div class="info-item"><span class="info-label">Adres:</span> ${patient.adres || '-'}</div>
                <div class="info-item"><span class="info-label">Deri Tipi:</span> ${patient.deri_tipi || '-'}</div>
                <div class="info-item"><span class="info-label">Ek Hastalıklar:</span> ${patient.ek_hastaliklar || '-'}</div>
                <div class="info-item"><span class="info-label">İlaçlar:</span> ${patient.kullandigi_ilaclar || '-'}</div>
                <div class="info-item"><span class="info-label">Notlar:</span> ${patient.notlar || '-'}</div>
            `;

            loadGroupedSessions(id);
            loadGroupedLaserSessions(id);
            loadCollections(id);

            document.getElementById('patient-detail-modal').style.display = 'block';
        }

        function deletePatient(id) {
            if (!confirm('Bu hastayı silmek istediğinizden emin misiniz?')) return;
            let patients = JSON.parse(localStorage.getItem('patients') || '[]');
            patients = patients.filter(p => p.id !== id);
            localStorage.setItem('patients', JSON.stringify(patients));

            // İlgili tüm işlemleri, lazer kayıtlarını ve tahsilatları da sil
            let sessions = JSON.parse(localStorage.getItem('patient_sessions') || '[]');
            sessions = sessions.filter(s => s.patient_id !== id);
            localStorage.setItem('patient_sessions', JSON.stringify(sessions));

            let lasers = JSON.parse(localStorage.getItem('patient_laser_sessions') || '[]');
            lasers = lasers.filter(l => l.patient_id !== id);
            localStorage.setItem('patient_laser_sessions', JSON.stringify(lasers));

            let collections = JSON.parse(localStorage.getItem('collections') || '[]');
            collections = collections.filter(c => c.patient_id !== id);
            localStorage.setItem('collections', JSON.stringify(collections));

            let appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            appointments = appointments.filter(a => a.patient_id !== id);
            localStorage.setItem('appointments', JSON.stringify(appointments));

            loadPatients();
            alert('Hasta ve tüm kayıtları silindi.');
        }

        // Genel İşlemler (Gruplu)
        function loadGroupedSessions(patientId) {
            const sessions = JSON.parse(localStorage.getItem('patient_sessions') || '[]');
            const patientSessions = sessions.filter(s => s.patient_id === patientId);
            const collections = JSON.parse(localStorage.getItem('collections') || '[]');

            const grouped = {};
            patientSessions.forEach(session => {
                const date = session.tarih;
                if (!grouped[date]) grouped[date] = [];
                grouped[date].push(session);
            });

            const container = document.getElementById('sessions-grouped');
            container.innerHTML = '';

            if (patientSessions.length === 0) {
                container.innerHTML = '<p style="text-align:center; color:#7f8c8d; padding:20px;">Henüz genel işlem girilmemiş.</p>';
                return;
            }

            Object.keys(grouped).sort((a,b) => new Date(b) - new Date(a)).forEach(date => {
                const daySessions = grouped[date];
                let totalAmount = 0;
                let totalPaid = 0;

                daySessions.forEach(session => {
                    totalAmount += session.ucret || 0;
                    const sessionPayments = collections.filter(c => c.session_id === session.id);
                    sessionPayments.forEach(p => totalPaid += p.tutar);
                });

                const groupDiv = document.createElement('div');
                groupDiv.className = 'date-group';

                groupDiv.innerHTML = `
                    <div class="date-group-header">
                        <div class="date-group-title">📅 ${formatDate(date)}</div>
                        <div class="date-group-summary">
                            <strong>${daySessions.length} işlem</strong> | 
                            Toplam: <strong>${totalAmount.toFixed(2)} TL</strong> | 
                            Ödenen: <strong>${totalPaid.toFixed(2)} TL</strong> | 
                            Kalan: <strong class="${totalAmount - totalPaid > 0 ? 'badge-warning' : 'badge-success'}">${(totalAmount - totalPaid).toFixed(2)} TL</strong>
                        </div>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>İşlem</th>
                                <th>Bölge</th>
                                <th>Ürün</th>
                                <th>Tutar</th>
                                <th>Ödenen</th>
                                <th>Kalan</th>
                                <th>Personel</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${daySessions.map(session => {
                                const sessionPayments = collections.filter(c => c.session_id === session.id);
                                const paid = sessionPayments.reduce((sum, p) => sum + p.tutar, 0);
                                const remaining = (session.ucret || 0) - paid;
                                return `
                                    <tr>
                                        <td>${session.islem_tipi}</td>
                                        <td>${session.bolge}</td>
                                        <td>${session.kullanilan_urun}</td>
                                        <td><strong>${(session.ucret || 0).toFixed(2)} TL</strong></td>
                                        <td>${paid.toFixed(2)} TL</td>
                                        <td><strong class="${remaining > 0 ? 'badge-warning' : 'badge-success'}">${remaining.toFixed(2)} TL</strong></td>
                                        <td>${session.uygulayan_personel}</td>
                                        <td>
                                            ${remaining > 0 ? 
                                                `<button class="payment-btn" onclick="openPaymentModal(${session.id}, 'genel', '${session.islem_tipi}', '${session.tarih}', ${session.ucret || 0}, ${paid})">Öde</button>` 
                                                : `<span class="paid-badge">Ödendi</span>`}
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                `;
                container.appendChild(groupDiv);
            });
        }

        // Lazer İşlemleri (Gruplu)
        function loadGroupedLaserSessions(patientId) {
            const lasers = JSON.parse(localStorage.getItem('patient_laser_sessions') || '[]');
            const patientLasers = lasers.filter(l => l.patient_id === patientId);
            const collections = JSON.parse(localStorage.getItem('collections') || '[]');

            const grouped = {};
            patientLasers.forEach(laser => {
                const date = laser.tarih;
                if (!grouped[date]) grouped[date] = [];
                grouped[date].push(laser);
            });

            const container = document.getElementById('laser-grouped');
            container.innerHTML = '';

            if (patientLasers.length === 0) {
                container.innerHTML = '<p style="text-align:center; color:#7f8c8d; padding:20px;">Henüz lazer işlemi girilmemiş.</p>';
                return;
            }

            Object.keys(grouped).sort((a,b) => new Date(b) - new Date(a)).forEach(date => {
                const dayLasers = grouped[date];
                let totalAmount = 0;
                let totalPaid = 0;

                dayLasers.forEach(laser => {
                    totalAmount += laser.ucret || 0;
                    const laserPayments = collections.filter(c => c.laser_session_id === laser.id);
                    laserPayments.forEach(p => totalPaid += p.tutar);
                });

                const groupDiv = document.createElement('div');
                groupDiv.className = 'date-group';

                groupDiv.innerHTML = `
                    <div class="date-group-header">
                        <div class="date-group-title">📅 ${formatDate(date)}</div>
                        <div class="date-group-summary">
                            <strong>${dayLasers.length} işlem</strong> | 
                            Toplam: <strong>${totalAmount.toFixed(2)} TL</strong> | 
                            Ödenen: <strong>${totalPaid.toFixed(2)} TL</strong> | 
                            Kalan: <strong class="${totalAmount - totalPaid > 0 ? 'badge-warning' : 'badge-success'}">${(totalAmount - totalPaid).toFixed(2)} TL</strong>
                        </div>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Bölge</th>
                                <th>Cihaz</th>
                                <th>Joule</th>
                                <th>Ücret</th>
                                <th>Ödenen</th>
                                <th>Kalan</th>
                                <th>Personel</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${dayLasers.map(laser => {
                                const laserPayments = collections.filter(c => c.laser_session_id === laser.id);
                                const paid = laserPayments.reduce((sum, p) => sum + p.tutar, 0);
                                const remaining = (laser.ucret || 0) - paid;
                                return `
                                    <tr>
                                        <td>${laser.bolge}</td>
                                        <td>${laser.cihaz}</td>
                                        <td>${laser.joule}</td>
                                        <td><strong>${(laser.ucret || 0).toFixed(2)} TL</strong></td>
                                        <td>${paid.toFixed(2)} TL</td>
                                        <td><strong class="${remaining > 0 ? 'badge-warning' : 'badge-success'}">${remaining.toFixed(2)} TL</strong></td>
                                        <td>${laser.uygulayan_personel}</td>
                                        <td>
                                            ${remaining > 0 ? 
                                                `<button class="payment-btn" onclick="openPaymentModal(${laser.id}, 'lazer', 'Lazer - ${laser.bolge}', '${laser.tarih}', ${laser.ucret || 0}, ${paid})">Öde</button>` 
                                                : `<span class="paid-badge">Ödendi</span>`}
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                `;
                container.appendChild(groupDiv);
            });
        }

        // Tahsilatlar
        function loadCollections(patientId) {
            const collections = JSON.parse(localStorage.getItem('collections') || '[]');
            const patientCollections = collections.filter(c => c.patient_id === patientId);
            const tableBody = document.getElementById('collections-list');
            tableBody.innerHTML = '';

            if (patientCollections.length === 0) {
                tableBody.innerHTML = `<tr><td colspan="4" style="text-align:center; padding:20px;">Henüz tahsilat yapılmamış.</td></tr>`;
                return;
            }

            patientCollections.forEach(collection => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDate(collection.tarih)}</td>
                    <td><strong>${collection.tutar.toFixed(2)} TL</strong></td>
                    <td>${collection.odeme_sekli}</td>
                    <td>${collection.aciklama}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Ödeme Modalı Aç
        function openPaymentModal(sessionId, type, description, date, total, paid) {
            document.getElementById('payment-session-id').value = sessionId;
            document.getElementById('payment-session-type').value = type;
            document.getElementById('payment-session-date').value = formatDate(date);
            document.getElementById('payment-session-type-display').value = description;
            document.getElementById('payment-total').value = total;
            document.getElementById('payment-paid').value = paid;
            document.getElementById('payment-amount').value = (total - paid).toFixed(2);
            document.getElementById('payment-modal').style.display = 'block';
        }

        // Ödemeyi Kaydet
        function savePayment() {
            const sessionId = parseInt(document.getElementById('payment-session-id').value);
            const type = document.getElementById('payment-session-type').value;
            const amount = parseFloat(document.getElementById('payment-amount').value);
            const method = document.getElementById('payment-method').value;
            const description = document.getElementById('payment-description').value || 'Ödeme';

            if (isNaN(amount) || amount <= 0) {
                alert('Lütfen geçerli bir tutar girin.');
                return;
            }

            const collections = JSON.parse(localStorage.getItem('collections') || '[]');
            const newCollection = {
                id: Date.now(),
                patient_id: currentPatientId,
                session_id: type === 'genel' ? sessionId : null,
                laser_session_id: type === 'lazer' ? sessionId : null,
                tarih: new Date().toISOString().split('T')[0],
                tutar: amount,
                odeme_sekli: method,
                aciklama: description,
                kasaya_islendi_mi: true,
                created_at: new Date().toISOString()
            };

            collections.push(newCollection);
            localStorage.setItem('collections', JSON.stringify(collections));

            closeModal('payment-modal');
            loadGroupedSessions(currentPatientId);
            loadGroupedLaserSessions(currentPatientId);
            loadCollections(currentPatientId);
            alert('Ödeme kaydedildi!');
        }

        // Genel İşlem Kaydet (Ödeme zorunlu değil)
        function saveSession() {
            const sessions = JSON.parse(localStorage.getItem('patient_sessions') || '[]');
            const newSession = {
                id: Date.now(),
                patient_id: parseInt(document.getElementById('current-patient-id').value),
                tarih: document.getElementById('session-date').value,
                islem_tipi: document.getElementById('session-type').value,
                bolge: document.getElementById('session-area').value,
                kullanilan_urun: document.getElementById('session-product').value,
                aciklama: document.getElementById('session-desc').value,
                ucret: parseFloat(document.getElementById('session-fee').value) || 0,
                uygulayan_personel: document.getElementById('session-staff').value,
                created_at: new Date().toISOString()
            };
            sessions.push(newSession);
            localStorage.setItem('patient_sessions', JSON.stringify(sessions));

            closeModal('session-modal');
            loadGroupedSessions(currentPatientId);
            alert('İşlem kaydedildi! (Ödeme daha sonra yapılabilir)');
        }

        // Lazer İşlem Kaydet
        function saveLaserSession() {
            const lasers = JSON.parse(localStorage.getItem('patient_laser_sessions') || '[]');
            const newLaser = {
                id: Date.now(),
                patient_id: parseInt(document.getElementById('laser-patient-id').value),
                tarih: document.getElementById('laser-date').value,
                bolge: document.getElementById('laser-area').value,
                cihaz: document.getElementById('laser-device').value,
                joule: document.getElementById('laser-joule').value,
                percent: document.getElementById('laser-percent').value,
                spot: document.getElementById('laser-spot').value,
                time: document.getElementById('laser-time').value,
                baslik: document.getElementById('laser-title').value,
                uygulayan_personel: document.getElementById('laser-staff').value,
                ucret: parseFloat(document.getElementById('laser-fee').value) || 0,
                created_at: new Date().toISOString()
            };
            lasers.push(newLaser);
            localStorage.setItem('patient_laser_sessions', JSON.stringify(lasers));

            closeModal('laser-modal');
            loadGroupedLaserSessions(currentPatientId);
            alert('Lazer işlemi kaydedildi! (Ödeme daha sonra yapılabilir)');
        }

        // Randevular
        function loadAppointments() {
            const appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            const patients = JSON.parse(localStorage.getItem('patients') || '[]');
            const tableBody = document.getElementById('appointments-list');
            tableBody.innerHTML = '';

            const today = new Date().toISOString().split('T')[0];
            let dailyCount = 0;

            appointments.forEach(app => {
                const patient = patients.find(p => p.id === app.patient_id);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${patient ? patient.ad_soyad : 'Bilinmiyor'}</td>
                    <td>${formatDate(app.tarih)}</td>
                    <td>${app.saat}</td>
                    <td>${app.islem_tipi}</td>
                    <td>${app.durum}</td>
                    <td>
                        <button onclick="editAppointment(${app.id})">Düzenle</button>
                        <button onclick="deleteAppointment(${app.id})" class="btn-danger">Sil</button>
                    </td>
                `;
                tableBody.appendChild(row);

                if (app.tarih === today) dailyCount++;
            });

            document.getElementById('daily-appointments').innerText = dailyCount;
        }

        function populatePatientSelect() {
            const select = document.getElementById('appointment-patient');
            select.innerHTML = '<option value="">Seçiniz</option>';
            const patients = JSON.parse(localStorage.getItem('patients') || '[]');
            patients.forEach(p => {
                const option = document.createElement('option');
                option.value = p.id;
                option.textContent = p.ad_soyad;
                select.appendChild(option);
            });
        }

        function saveAppointment() {
            const appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            const newAppointment = {
                id: Date.now(),
                patient_id: parseInt(document.getElementById('appointment-patient').value),
                tarih: document.getElementById('appointment-date').value,
                saat: document.getElementById('appointment-time').value,
                islem_tipi: document.getElementById('appointment-procedure').value,
                bolge: document.getElementById('appointment-area').value,
                durum: document.getElementById('appointment-status').value,
                notlar: document.getElementById('appointment-notes').value
            };
            appointments.push(newAppointment);
            localStorage.setItem('appointments', JSON.stringify(appointments));
            closeModal('appointment-modal');
            loadAppointments();
            alert('Randevu kaydedildi!');
        }

        function deleteAppointment(id) {
            if (!confirm('Randevuyu silmek istediğinizden emin misiniz?')) return;
            let appointments = JSON.parse(localStorage.getItem('appointments') || '[]');
            appointments = appointments.filter(a => a.id !== id);
            localStorage.setItem('appointments', JSON.stringify(appointments));
            loadAppointments();
        }

        // Dashboard
        function loadDashboard() {
            const collections = JSON.parse(localStorage.getItem('collections') || '[]');
            const today = new Date().toISOString().split('T')[0];
            const todayIncome = collections
                .filter(c => c.tarih === today)
                .reduce((sum, c) => sum + c.tutar, 0);
            document.getElementById('daily-income').innerText = `${todayIncome.toFixed(2)} TL`;
        }

        // Raporlar
        function generateReport() {
            const reportType = document.getElementById('report-type').value;
            const output = document.getElementById('report-output');
            output.innerHTML = '<p>Rapor hazırlanıyor...</p>';

            if (reportType === 'gelir') {
                const collections = JSON.parse(localStorage.getItem('collections') || '[]');
                const total = collections.reduce((sum, c) => sum + c.tutar, 0);
                const byPayment = {};
                collections.forEach(c => {
                    byPayment[c.odeme_sekli] = (byPayment[c.odeme_sekli] || 0) + c.tutar;
                });

                let html = `<h4>📊 Toplam Gelir: <strong>${total.toFixed(2)} TL</strong></h4><ul style="margin:20px 0;">`;
                for (let method in byPayment) {
                    html += `<li><strong>${method}:</strong> ${byPayment[method].toFixed(2)} TL</li>`;
                }
                html += '</ul>';
                output.innerHTML = html;
            } else if (reportType === 'hasta') {
                const patients = JSON.parse(localStorage.getItem('patients') || '[]');
                const sessions = JSON.parse(localStorage.getItem('patient_sessions') || '[]');
                const lasers = JSON.parse(localStorage.getItem('patient_laser_sessions') || '[]');

                let html = '<h4>📋 Hasta İşlem Raporu</h4><ul style="margin:20px 0;">';
                patients.forEach(p => {
                    const patientSessions = sessions.filter(s => s.patient_id === p.id).length;
                    const patientLasers = lasers.filter(l => l.patient_id === p.id).length;
                    html += `<li><strong>${p.ad_soyad}:</strong> ${patientSessions} genel işlem, ${patientLasers} lazer işlem</li>`;
                });
                html += '</ul>';
                output.innerHTML = html;
            } else if (reportType === 'borc') {
                const patients = JSON.parse(localStorage.getItem('patients') || '[]');
                const sessions = JSON.parse(localStorage.getItem('patient_sessions') || '[]');
                const lasers = JSON.parse(localStorage.getItem('patient_laser_sessions') || '[]');
                const collections = JSON.parse(localStorage.getItem('collections') || '[]');

                let html = '<h4>💰 Borç Raporu</h4><table style="width:100%; border-collapse:collapse; margin:20px 0; background:white; box-shadow:0 2px 5px rgba(0,0,0,0.05);">';
                html += '<thead><tr style="background:#2c3e50; color:white;"><th>Hasta</th><th>Toplam Tutar</th><th>Ödenen</th><th>Kalan Borç</th></tr></thead><tbody>';

                patients.forEach(p => {
                    let totalDebt = 0;
                    let totalPaid = 0;

                    // Genel işlemler
                    const patientSessions = sessions.filter(s => s.patient_id === p.id);
                    patientSessions.forEach(s => {
                        totalDebt += s.ucret || 0;
                        const payments = collections.filter(c => c.session_id === s.id);
                        payments.forEach(payment => totalPaid += payment.tutar);
                    });

                    // Lazer işlemler
                    const patientLasers = lasers.filter(l => l.patient_id === p.id);
                    patientLasers.forEach(l => {
                        totalDebt += l.ucret || 0;
                        const payments = collections.filter(c => c.laser_session_id === l.id);
                        payments.forEach(payment => totalPaid += payment.tutar);
                    });

                    const remaining = totalDebt - totalPaid;
                    if (remaining > 0) {
                        html += `
                            <tr style="border-bottom:1px solid #eee;">
                                <td><strong>${p.ad_soyad}</strong></td>
                                <td>${totalDebt.toFixed(2)} TL</td>
                                <td>${totalPaid.toFixed(2)} TL</td>
                                <td style="color:${remaining > 0 ? '#e74c3c' : '#27ae60'}; font-weight:bold;">${remaining.toFixed(2)} TL</td>
                            </tr>
                        `;
                    }
                });

                html += '</tbody></table>';
                output.innerHTML = html;
            }
        }

        // Yardımcı Fonksiyonlar
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('tr-TR');
        }

        function filterPatients() {
            const term = document.getElementById('search-patient').value.toLowerCase();
            const rows = document.querySelectorAll('#patients-list tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(term) ? '' : 'none';
            });
        }

        function editAppointment(id) {
            alert('Randevu düzenleme henüz geliştirilmedi. ID: ' + id);
        }
    </script>
</body>
</html>