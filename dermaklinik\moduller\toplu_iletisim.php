<?php
// Toplu iletişim geçmişini çek
$stmt = $pdo->prepare("
    SELECT 
        ti.*,
        COUNT(tip.patient_id) as gonderilen_sayi
    FROM toplu_iletisim ti
    LEFT JOIN toplu_iletisim_patients tip ON ti.id = tip.iletisim_id
    GROUP BY ti.id
    ORDER BY ti.created_at DESC
    LIMIT 20
");
$stmt->execute();
$communication_history = $stmt->fetchAll();
?>

<h2>Toplu İletişim</h2>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
    <!-- SMS Gönder -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">📱 Toplu SMS</h3>
        <p style="color: #7f8c8d; margin-bottom: 15px;">Seçili hastalara toplu SMS gönderin.</p>
        <button class="btn-warning" onclick="openBulkSmsModal()" style="width: 100%; padding: 12px;">
            📱 SMS Gönder
        </button>
    </div>

    <!-- Email Gönder -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">📧 Toplu E-posta</h3>
        <p style="color: #7f8c8d; margin-bottom: 15px;">Seçili hastalara toplu e-posta gönderin.</p>
        <button class="btn-info" onclick="openBulkEmailModal()" style="width: 100%; padding: 12px;">
            📧 E-posta Gönder
        </button>
    </div>
</div>

<!-- İletişim Geçmişi -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); margin-top: 20px;">
    <h3 style="margin-bottom: 15px; color: #2c3e50;">📋 İletişim Geçmişi</h3>
    
    <?php if (empty($communication_history)): ?>
        <p style="color: #7f8c8d; text-align: center; padding: 20px;">Henüz toplu iletişim kaydı yok.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>Tarih</th>
                    <th>Tip</th>
                    <th>Konu/İçerik</th>
                    <th>Gönderilen</th>
                    <th>Durum</th>
                    <th>İşlemler</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($communication_history as $comm): ?>
                <tr>
                    <td><?= formatDate($comm['created_at']) ?></td>
                    <td>
                        <span class="badge <?= $comm['tip'] === 'SMS' ? 'badge-warning' : 'badge-info' ?>">
                            <?= $comm['tip'] ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($comm['tip'] === 'SMS'): ?>
                            <?= htmlspecialchars(substr($comm['mesaj'], 0, 50)) ?>...
                        <?php else: ?>
                            <strong><?= htmlspecialchars($comm['konu']) ?></strong>
                        <?php endif; ?>
                    </td>
                    <td><?= $comm['gonderilen_sayi'] ?> hasta</td>
                    <td>
                        <span class="badge <?= $comm['durum'] === 'Gönderildi' ? 'badge-success' : 'badge-warning' ?>">
                            <?= $comm['durum'] ?>
                        </span>
                    </td>
                    <td>
                        <div class="dropdown">
                            <button class="dropdown-btn" onclick="toggleDropdown(this)">⋮</button>
                            <div class="dropdown-content">
                                <a href="#" onclick="viewCommunicationDetails(<?= $comm['id'] ?>)">Detay</a>
                                <a href="#" onclick="resendCommunication(<?= $comm['id'] ?>)" class="text-warning">Tekrar Gönder</a>
                                <a href="#" onclick="deleteCommunication(<?= $comm['id'] ?>)" class="text-danger">Sil</a>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>

<!-- İletişim Detay Modal -->
<div id="communicationDetailModal" class="modal">
    <div class="modal-content" style="max-width:600px;">
        <div class="modal-header">
            <h2>İletişim Detayı</h2>
            <span class="close-btn" onclick="closeModal('communicationDetailModal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="communicationDetailContent">
                <!-- Detay içeriği buraya yüklenecek -->
            </div>
        </div>
    </div>
</div>

<script>
function viewCommunicationDetails(id) {
    fetch('actions/toplu_iletisim_actions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_communication_details&id=' + id
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('communicationDetailContent').innerHTML = data.html;
            openModal('communicationDetailModal');
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

function resendCommunication(id) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu iletişimi tekrar göndermek istediğinizden emin misiniz?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Evet, Gönder!',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
    
            fetch('actions/toplu_iletisim_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=resend_communication&id=' + id
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: 'Bir hata oluştu!',
                    confirmButtonText: 'Tamam'
                });
            });
        }
    });
}

function deleteCommunication(id) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu iletişim kaydını silmek istediğinizden emin misiniz?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
    
            fetch('actions/toplu_iletisim_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=delete_communication&id=' + id
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: 'Bir hata oluştu!',
                    confirmButtonText: 'Tamam'
                });
            });
        }
    });
}
</script>
