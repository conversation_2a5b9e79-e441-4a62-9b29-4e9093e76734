<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_patient') {
        $ad_soyad = trim($_POST['ad_soyad']);
        $tc = trim($_POST['tc_kimlik_no']);
        $dogum_tarihi = $_POST['dogum_tarihi'] ?: null;
        $cinsiyet = $_POST['cinsiyet'];
        $telefon = trim($_POST['telefon']);
        $email = trim($_POST['email']);
        $adres = trim($_POST['adres']);
        $deri_tipi = trim($_POST['deri_tipi']);
        $ek_hastaliklar = trim($_POST['ek_hastaliklar']);
        $ilaclar = trim($_POST['kullandigi_ilaclar']);
        $notlar = trim($_POST['notlar']);

        try {
            $stmt = $pdo->prepare("INSERT INTO patients (ad_soyad, tc_kimlik_no, dogum_tarihi, cinsiyet, telefon, email, adres, deri_tipi, ek_hastaliklar, kullandigi_ilaclar, notlar) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$ad_soyad, $tc, $dogum_tarihi, $cinsiyet, $telefon, $email, $adres, $deri_tipi, $ek_hastaliklar, $ilaclar, $notlar]);
            echo json_encode(['success' => true, 'message' => 'Hasta başarıyla eklendi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

