<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_appointment') {
        $patient_id = (int)$_POST['patient_id'];
        $tarih = $_POST['tarih'];
        $saat = $_POST['saat'];
        $islem_tipi = trim($_POST['islem_tipi']);
        $bolge = trim($_POST['bolge']);
        $durum = $_POST['durum'];
        $notlar = trim($_POST['notlar']);

        try {
            $stmt = $pdo->prepare("INSERT INTO appointments (patient_id, tarih, saat, islem_tipi, bolge, durum, notlar, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$patient_id, $tarih, $saat, $islem_tipi, $bolge, $durum, $notlar]);
            echo json_encode(['success' => true, 'message' => 'Randevu başarıyla eklendi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'delete_appointment') {
        $id = (int)$_POST['id'];
        
        try {
            $stmt = $pdo->prepare("DELETE FROM appointments WHERE id = ?");
            $stmt->execute([$id]);
            echo json_encode(['success' => true, 'message' => 'Randevu başarıyla silindi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

