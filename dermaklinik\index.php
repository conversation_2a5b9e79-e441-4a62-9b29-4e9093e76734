<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

if (!isLoggedIn()) {
    redirect('login.php');
}

$active_tab = $_GET['tab'] ?? 'dashboard';
?>

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= APP_NAME ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>

<?php include 'moduller/sidebar.php'; ?>

<div class="main-content" id="mainContent">
    <?php include 'moduller/header.php'; ?>
    <div class="container">
        <?php
        $tabs = [
            'dashboard' => 'moduller/dashboard.php',
            'hastalar' => 'moduller/hastalar.php',
            'hasta_detay' => 'moduller/hasta_detay.php',
            'randevular' => 'moduller/randevular.php',
            'raporlar' => 'moduller/raporlar.php',
            'tanimlamalar' => 'moduller/tanimlamalar.php',
            'toplu_iletisim' => 'moduller/toplu_iletisim.php',
        ];

        if (isset($tabs[$active_tab]) && file_exists($tabs[$active_tab])) {
            include $tabs[$active_tab];
        } else {
            include 'moduller/dashboard.php';
        }
        ?>
    </div>
</div>

<?php include 'modals/modals.php'; ?>

<script src="assets/js/main.js"></script>
</body>
</html>
