<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'send_bulk_sms') {
        $patient_ids = json_decode($_POST['patient_ids'], true);
        $mesaj = trim($_POST['mesaj']);

        try {
            // Toplu iletişim kaydı oluştur
            $stmt = $pdo->prepare("INSERT INTO toplu_iletisim (tip, mesaj, durum, created_at) VALUES ('SMS', ?, 'Gönderildi', NOW())");
            $stmt->execute([$mesaj]);
            $iletisim_id = $pdo->lastInsertId();

            // Hasta kayıtlarını ekle
            foreach ($patient_ids as $patient_id) {
                $stmt = $pdo->prepare("INSERT INTO toplu_iletisim_patients (iletisim_id, patient_id) VALUES (?, ?)");
                $stmt->execute([$iletisim_id, $patient_id]);
            }

            echo json_encode(['success' => true, 'message' => 'SMS başarıyla gönderildi. (' . count($patient_ids) . ' hasta)']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'send_bulk_email') {
        $patient_ids = json_decode($_POST['patient_ids'], true);
        $konu = trim($_POST['konu']);
        $icerik = trim($_POST['icerik']);

        try {
            // Toplu iletişim kaydı oluştur
            $stmt = $pdo->prepare("INSERT INTO toplu_iletisim (tip, konu, mesaj, durum, created_at) VALUES ('Email', ?, ?, 'Gönderildi', NOW())");
            $stmt->execute([$konu, $icerik]);
            $iletisim_id = $pdo->lastInsertId();

            // Hasta kayıtlarını ekle
            foreach ($patient_ids as $patient_id) {
                $stmt = $pdo->prepare("INSERT INTO toplu_iletisim_patients (iletisim_id, patient_id) VALUES (?, ?)");
                $stmt->execute([$iletisim_id, $patient_id]);
            }

            echo json_encode(['success' => true, 'message' => 'E-posta başarıyla gönderildi. (' . count($patient_ids) . ' hasta)']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'get_communication_details') {
        $id = (int)$_POST['id'];
        
        try {
            $stmt = $pdo->prepare("
                SELECT ti.*, p.ad_soyad, p.telefon, p.email
                FROM toplu_iletisim ti
                LEFT JOIN toplu_iletisim_patients tip ON ti.id = tip.iletisim_id
                LEFT JOIN patients p ON tip.patient_id = p.id
                WHERE ti.id = ?
            ");
            $stmt->execute([$id]);
            $details = $stmt->fetchAll();
            
            if (empty($details)) {
                echo json_encode(['success' => false, 'message' => 'Kayıt bulunamadı.']);
                exit;
            }
            
            $html = '<div class="info-grid">';
            $html .= '<div class="info-item"><span class="info-label">Tip:</span> ' . $details[0]['tip'] . '</div>';
            $html .= '<div class="info-item"><span class="info-label">Tarih:</span> ' . formatDate($details[0]['created_at']) . '</div>';
            $html .= '<div class="info-item"><span class="info-label">Durum:</span> ' . $details[0]['durum'] . '</div>';
            if ($details[0]['konu']) {
                $html .= '<div class="info-item"><span class="info-label">Konu:</span> ' . htmlspecialchars($details[0]['konu']) . '</div>';
            }
            $html .= '</div>';
            
            $html .= '<h4>Mesaj İçeriği:</h4>';
            $html .= '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">';
            $html .= '<pre>' . htmlspecialchars($details[0]['mesaj']) . '</pre>';
            $html .= '</div>';
            
            $html .= '<h4>Gönderilen Hastalar:</h4>';
            $html .= '<table style="font-size: 0.9rem;">';
            $html .= '<thead><tr><th>Hasta</th><th>Telefon</th><th>Email</th></tr></thead>';
            $html .= '<tbody>';
            foreach ($details as $detail) {
                if ($detail['ad_soyad']) {
                    $html .= '<tr>';
                    $html .= '<td>' . htmlspecialchars($detail['ad_soyad']) . '</td>';
                    $html .= '<td>' . htmlspecialchars($detail['telefon']) . '</td>';
                    $html .= '<td>' . htmlspecialchars($detail['email']) . '</td>';
                    $html .= '</tr>';
                }
            }
            $html .= '</tbody></table>';
            
            echo json_encode(['success' => true, 'html' => $html]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'delete_communication') {
        $id = (int)$_POST['id'];
        
        try {
            $pdo->beginTransaction();
            
            // İlgili hasta kayıtlarını sil
            $stmt = $pdo->prepare("DELETE FROM toplu_iletisim_patients WHERE iletisim_id = ?");
            $stmt->execute([$id]);
            
            // Ana kaydı sil
            $stmt = $pdo->prepare("DELETE FROM toplu_iletisim WHERE id = ?");
            $stmt->execute([$id]);
            
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'İletişim kaydı başarıyla silindi.']);
        } catch (Exception $e) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

