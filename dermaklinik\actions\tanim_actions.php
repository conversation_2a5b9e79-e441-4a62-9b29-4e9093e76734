<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Hata ayıklama için
error_log("Tanım action çağrıldı: " . print_r($_POST, true));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_definition') {
        $type = $_POST['type'];
        $value = trim($_POST['value']);

        error_log("Tanım ekleniyor - Type: $type, Value: $value");

        try {
            // Önce tablo var mı kontrol et
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'definitions'");
            $stmt->execute();
            $tableExists = $stmt->fetch();
            
            if (!$tableExists) {
                // Tablo yoksa oluştur
                $pdo->exec("CREATE TABLE definitions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    type VARCHAR(50) NOT NULL,
                    value VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");
                error_log("Definitions tablosu oluşturuldu");
            }

            $stmt = $pdo->prepare("INSERT INTO definitions (type, value, created_at) VALUES (?, ?, NOW())");
            $stmt->execute([$type, $value]);
            error_log("Tanım başarıyla eklendi");
            echo json_encode(['success' => true, 'message' => 'Tanım başarıyla eklendi.']);
        } catch (Exception $e) {
            error_log("Tanım ekleme hatası: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'delete_definition') {
        $id = (int)$_POST['id'];
        
        try {
            $stmt = $pdo->prepare("DELETE FROM definitions WHERE id = ?");
            $stmt->execute([$id]);
            echo json_encode(['success' => true, 'message' => 'Tanım başarıyla silindi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>
