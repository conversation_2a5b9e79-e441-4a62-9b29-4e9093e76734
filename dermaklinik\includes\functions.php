<?php
function formatDate($date) {
    if (!$date) return '-';
    return date('d.m.Y', strtotime($date));
}

function getDefinitions($type) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT value FROM definitions WHERE type = ? ORDER BY value ASC");
    $stmt->execute([$type]);
    return $stmt->fetchAll();
}

function getPatientDebt($patient_id) {
    global $pdo;
    
    $debt = 0;
    
    // Genel işlemler
    $stmt = $pdo->prepare("SELECT SUM(ucret) as total FROM patient_sessions WHERE patient_id = ?");
    $stmt->execute([$patient_id]);
    $row = $stmt->fetch();
    $debt += $row['total'] ?? 0;
    
    // Lazer işlemler
    $stmt = $pdo->prepare("SELECT SUM(ucret) as total FROM patient_laser_sessions WHERE patient_id = ?");
    $stmt->execute([$patient_id]);
    $row = $stmt->fetch();
    $debt += $row['total'] ?? 0;
    
    // Ödemeler
    $stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE patient_id = ?");
    $stmt->execute([$patient_id]);
    $row = $stmt->fetch();
    $paid = $row['total'] ?? 0;
    
    return $debt - $paid;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function redirect($url) {
    header("Location: " . $url);
    exit;
}
?>

