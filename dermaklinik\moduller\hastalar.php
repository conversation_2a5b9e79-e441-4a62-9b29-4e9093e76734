<?php
$stmt = $pdo->prepare("SELECT * FROM patients ORDER BY ad_soyad ASC");
$stmt->execute();
$patients = $stmt->fetchAll();
?>

<h2>Hastalar</h2>
<div class="bulk-actions">
    <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
    <label for="select-all">Tü<PERSON>ü<PERSON><PERSON></label>
    <button class="btn-warning" onclick="openBulkSmsModal()">Toplu SMS Gönder</button>
    <button class="btn-info" onclick="openBulkEmailModal()">Toplu E-posta Gönder</button>
</div>
<div class="form-group" style="max-width: 400px;">
    <input type="text" id="search-patient" placeholder="Ad, TC veya Telefon ile ara..." oninput="filterPatients()" style="padding: 12px; font-size: 1rem;">
</div>
<button class="btn-primary" onclick="openModal('hastaEkleModal')">+ <PERSON><PERSON><PERSON></button>

<table>
    <thead>
        <tr>
            <th><input type="checkbox" id="header-checkbox" onchange="toggleSelectAll()"></th>
            <th>Ad Soyad</th>
            <th>TC</th>
            <th>Telefon</th>
            <th>Borç</th>
            <th>İşlemler</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($patients as $p): ?>
        <tr class="selectable-row">
            <td><input type="checkbox" class="patient-checkbox" value="<?= $p['id'] ?>"></td>
            <td><?= htmlspecialchars($p['ad_soyad']) ?></td>
            <td><?= htmlspecialchars($p['tc_kimlik_no']) ?></td>
            <td><?= htmlspecialchars($p['telefon']) ?></td>
            <td><?= number_format(getPatientDebt($p['id']), 2, ',', '.') ?> TL</td>
            <td>
                <button class="btn-info" onclick="window.location.href='?tab=hasta_detay&id=<?= $p['id'] ?>'">Detay</button>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>

