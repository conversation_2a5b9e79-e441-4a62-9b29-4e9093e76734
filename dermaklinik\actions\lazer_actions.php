<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_laser_session') {
        $patient_id = (int)$_POST['patient_id'];
        $tarih = $_POST['tarih'];
        $bolge = trim($_POST['bolge']);
        $cihaz = trim($_POST['cihaz']);
        $joule = trim($_POST['joule']);
        $percent = (int)$_POST['percent'];
        $spot = trim($_POST['spot']);
        $time = trim($_POST['time']);
        $baslik = trim($_POST['baslik']);
        $ucret = (float)$_POST['ucret'];
        $uygulayan_personel = trim($_POST['uygulayan_personel']);

        try {
            $stmt = $pdo->prepare("INSERT INTO patient_laser_sessions (patient_id, tarih, bolge, cihaz, joule, percent, spot, time, baslik, ucret, uygulayan_personel, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$patient_id, $tarih, $bolge, $cihaz, $joule, $percent, $spot, $time, $baslik, $ucret, $uygulayan_personel]);
            echo json_encode(['success' => true, 'message' => 'Lazer işlemi başarıyla eklendi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

