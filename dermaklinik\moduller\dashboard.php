<?php
// Dashboard verilerini çek
$today = date('Y-m-d');

// Günlük randevu sayısı
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM appointments WHERE tarih = ?");
$stmt->execute([$today]);
$daily_appointments = $stmt->fetch()['count'];

// Günlük gelir
$stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE tarih = ?");
$stmt->execute([$today]);
$daily_income = $stmt->fetch()['total'] ?? 0;

// Toplam hasta sayısı
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM patients");
$stmt->execute();
$total_patients = $stmt->fetch()['count'];

// Toplam borç
$stmt = $pdo->prepare("
    SELECT 
        (SELECT COALESCE(SUM(ucret), 0) FROM patient_sessions) + 
        (SELECT COALESCE(SUM(ucret), 0) FROM patient_laser_sessions) - 
        (SELECT COALESCE(SUM(tutar), 0) FROM collections) as total_debt
");
$stmt->execute();
$total_debt = $stmt->fetch()['total_debt'] ?? 0;
?>

<h2>Dashboard</h2>

<div class="dashboard-cards">
    <div class="dashboard-card primary">
        <h4>Günlük Randevular</h4>
        <h2><?= $daily_appointments ?></h2>
    </div>
    <div class="dashboard-card success">
        <h4>Günlük Gelir</h4>
        <h2><?= number_format($daily_income, 2, ',', '.') ?> TL</h2>
    </div>
    <div class="dashboard-card warning">
        <h4>Toplam Hasta</h4>
        <h2><?= $total_patients ?></h2>
    </div>
    <div class="dashboard-card" style="border-left-color: #e74c3c;">
        <h4>Toplam Borç</h4>
        <h2><?= number_format($total_debt, 2, ',', '.') ?> TL</h2>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
    <!-- Son Eklenen Hastalar -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">📋 Son Eklenen Hastalar</h3>
        <?php
        $stmt = $pdo->prepare("SELECT * FROM patients ORDER BY created_at DESC LIMIT 5");
        $stmt->execute();
        $recent_patients = $stmt->fetchAll();
        ?>
        <?php if (empty($recent_patients)): ?>
            <p style="color: #7f8c8d; text-align: center; padding: 20px;">Henüz hasta eklenmemiş.</p>
        <?php else: ?>
            <table style="font-size: 0.9rem;">
                <thead>
                    <tr>
                        <th>Ad Soyad</th>
                        <th>Telefon</th>
                        <th>İşlem</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_patients as $patient): ?>
                    <tr>
                        <td><?= htmlspecialchars($patient['ad_soyad']) ?></td>
                        <td><?= htmlspecialchars($patient['telefon']) ?></td>
                        <td>
                            <button class="btn-info" onclick="window.location.href='?tab=hasta_detay&id=<?= $patient['id'] ?>'">Detay</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <!-- Bugünkü Randevular -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">📅 Bugünkü Randevular</h3>
        <?php
        $stmt = $pdo->prepare("
            SELECT a.*, p.ad_soyad 
            FROM appointments a 
            LEFT JOIN patients p ON a.patient_id = p.id 
            WHERE a.tarih = ? 
            ORDER BY a.saat ASC
        ");
        $stmt->execute([$today]);
        $today_appointments = $stmt->fetchAll();
        ?>
        <?php if (empty($today_appointments)): ?>
            <p style="color: #7f8c8d; text-align: center; padding: 20px;">Bugün randevu yok.</p>
        <?php else: ?>
            <table style="font-size: 0.9rem;">
                <thead>
                    <tr>
                        <th>Hasta</th>
                        <th>Saat</th>
                        <th>İşlem</th>
                        <th>Durum</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($today_appointments as $appointment): ?>
                    <tr>
                        <td><?= htmlspecialchars($appointment['ad_soyad']) ?></td>
                        <td><?= $appointment['saat'] ?></td>
                        <td><?= htmlspecialchars($appointment['islem_tipi']) ?></td>
                        <td>
                            <span class="badge <?= $appointment['durum'] === 'Tamamlandı' ? 'badge-success' : ($appointment['durum'] === 'İptal' ? 'badge-danger' : 'badge-warning') ?>">
                                <?= $appointment['durum'] ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<div style="margin-top: 20px; text-align: center;">
    <button class="btn-primary" onclick="window.location.href='?tab=hastalar'">Hasta Ekle</button>
    <button class="btn-info" onclick="window.location.href='?tab=randevular'">Randevu Ekle</button>
</div>

