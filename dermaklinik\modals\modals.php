<!-- <PERSON><PERSON> -->
<div id="hastaEkleModal" class="modal">
    <div class="modal-content" style="max-width:500px;">
        <div class="modal-header">
            <h2><PERSON><PERSON> <PERSON><PERSON></h2>
            <span class="close-btn" onclick="closeModal('hastaEkleModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="hastaEkleForm">
                <div class="form-group">
                    <label>Ad Soyad *</label>
                    <input type="text" name="ad_soyad" required>
                </div>
                <div class="form-group">
                    <label>TC Kimlik No *</label>
                    <input type="text" name="tc_kimlik_no" required>
                </div>
                <div class="form-group">
                    <label>Doğum Tarihi</label>
                    <input type="date" name="dogum_tarihi">
                </div>
                <div class="form-group">
                    <label>Cinsiyet</label>
                    <select name="cinsiyet">
                        <option value="Erkek">Erkek</option>
                        <option value="Kadın">Kadın</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Telefon</label>
                    <input type="text" name="telefon">
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" name="email">
                </div>
                <div class="form-group">
                    <label>Adres</label>
                    <textarea name="adres" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>Deri Tipi</label>
                    <input type="text" name="deri_tipi">
                </div>
                <div class="form-group">
                    <label>Ek Hastalıklar</label>
                    <textarea name="ek_hastaliklar" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>Kullandığı İlaçlar</label>
                    <textarea name="kullandigi_ilaclar" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>Notlar</label>
                    <textarea name="notlar" rows="2"></textarea>
                </div>
                <button type="submit" class="btn-primary">Kaydet</button>
                <button type="button" onclick="closeModal('hastaEkleModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Genel İşlem Modal -->
<div id="genelIslemModal" class="modal">
    <div class="modal-content" style="max-width:500px;">
        <div class="modal-header">
            <h2>Yeni İşlem Ekle</h2>
            <span class="close-btn" onclick="closeModal('genelIslemModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="genelIslemForm">
                <input type="hidden" name="patient_id" id="genelIslemPatientId">
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" name="tarih" required>
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" name="islem_tipi" placeholder="Aquapell, PRP vb." required>
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" name="bolge">
                </div>
                <div class="form-group">
                    <label>Kullanılan Ürün</label>
                    <input type="text" name="kullanilan_urun">
                </div>
                <div class="form-group">
                    <label>Ücret (TL)</label>
                    <input type="number" name="ucret" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label>Uygulayan Personel</label>
                    <input type="text" name="uygulayan_personel">
                </div>
                <div class="form-group">
                    <label>Açıklama</label>
                    <textarea name="aciklama" rows="2"></textarea>
                </div>
                <button type="submit" class="btn-primary">Kaydet</button>
                <button type="button" onclick="closeModal('genelIslemModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Lazer İşlem Modal -->
<div id="lazerIslemModal" class="modal">
    <div class="modal-content" style="max-width:600px;">
        <div class="modal-header">
            <h2>Yeni Lazer İşlemi</h2>
            <span class="close-btn" onclick="closeModal('lazerIslemModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="lazerIslemForm">
                <input type="hidden" name="patient_id" id="lazerIslemPatientId">
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" name="tarih" required>
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" name="bolge" placeholder="Yüz, Bacak, Kol" required>
                </div>
                <div class="form-group">
                    <label>Cihaz</label>
                    <input type="text" name="cihaz" placeholder="GentleLase vb.">
                </div>
                <div class="form-group">
                    <label>Joule</label>
                    <input type="text" name="joule">
                </div>
                <div class="form-group">
                    <label>% Oranı</label>
                    <input type="number" name="percent" min="0" max="100" placeholder="Opsiyonel">
                </div>
                <div class="form-group">
                    <label>Spot</label>
                    <input type="text" name="spot">
                </div>
                <div class="form-group">
                    <label>Time</label>
                    <input type="text" name="time">
                </div>
                <div class="form-group">
                    <label>Başlık</label>
                    <input type="text" name="baslik">
                </div>
                <div class="form-group">
                    <label>Ücret (TL)</label>
                    <input type="number" name="ucret" min="0" step="0.01">
                </div>
                <div class="form-group">
                    <label>Uygulayan Personel</label>
                    <input type="text" name="uygulayan_personel">
                </div>
                <button type="submit" class="btn-primary">Kaydet</button>
                <button type="button" onclick="closeModal('lazerIslemModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Randevu Modal -->
<div id="randevuModal" class="modal">
    <div class="modal-content" style="max-width:500px;">
        <div class="modal-header">
            <h2>Yeni Randevu</h2>
            <span class="close-btn" onclick="closeModal('randevuModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="randevuForm">
                <div class="form-group">
                    <label>Hasta</label>
                    <select name="patient_id" required>
                        <option value="">Seçiniz</option>
                        <?php
                        $stmt = $pdo->prepare("SELECT id, ad_soyad FROM patients ORDER BY ad_soyad ASC");
                        $stmt->execute();
                        $patients = $stmt->fetchAll();
                        foreach ($patients as $patient):
                        ?>
                        <option value="<?= $patient['id'] ?>"><?= htmlspecialchars($patient['ad_soyad']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label>Tarih</label>
                    <input type="date" name="tarih" required>
                </div>
                <div class="form-group">
                    <label>Saat</label>
                    <input type="time" name="saat" required>
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" name="islem_tipi">
                </div>
                <div class="form-group">
                    <label>Bölge</label>
                    <input type="text" name="bolge">
                </div>
                <div class="form-group">
                    <label>Durum</label>
                    <select name="durum">
                        <option value="Bekliyor">Bekliyor</option>
                        <option value="Tamamlandı">Tamamlandı</option>
                        <option value="İptal">İptal</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Notlar</label>
                    <textarea name="notlar" rows="2"></textarea>
                </div>
                <button type="submit" class="btn-primary">Kaydet</button>
                <button type="button" onclick="closeModal('randevuModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Ödeme Modal -->
<div id="odemeModal" class="modal">
    <div class="modal-content" style="max-width:450px;">
        <div class="modal-header" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);">
            <h2>💰 Ödeme Yap</h2>
            <span class="close-btn" onclick="closeModal('odemeModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="odemeForm">
                <input type="hidden" name="session_id" id="odemeSessionId">
                <input type="hidden" name="session_type" id="odemeSessionType">
                <input type="hidden" name="patient_id" id="odemePatientId">

                <div class="form-group">
                    <label>İşlem Tarihi</label>
                    <input type="text" id="odemeSessionDate" disabled style="background:#f5f5f5; color:#2c3e50; font-weight:bold;">
                </div>
                <div class="form-group">
                    <label>İşlem Tipi</label>
                    <input type="text" id="odemeSessionTypeDisplay" disabled style="background:#f5f5f5; color:#2c3e50; font-weight:bold;">
                </div>
                <div class="form-group">
                    <label>Toplam Tutar (TL)</label>
                    <input type="number" id="odemeTotal" disabled style="background:#f5f5f5; font-weight:bold; color:#e74c3c;">
                </div>
                <div class="form-group">
                    <label>Ödenen Tutar (TL)</label>
                    <input type="number" id="odemePaid" disabled style="background:#f5f5f5; font-weight:bold; color:#27ae60;">
                </div>
                <div class="form-group">
                    <label>Ödenecek Tutar (TL)</label>
                    <input type="number" name="tutar" id="odemeAmount" min="0" step="0.01" placeholder="Ödenecek miktar" required>
                </div>
                <div class="form-group">
                    <label>Ödeme Şekli</label>
                    <select name="odeme_sekli" required>
                        <option value="Nakit">Nakit</option>
                        <option value="Kredi Kartı">Kredi Kartı</option>
                        <option value="Havale">Havale</option>
                        <option value="Taksit">Taksit</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Açıklama</label>
                    <input type="text" name="aciklama" placeholder="Ödeme açıklaması (opsiyonel)">
                </div>
                <button type="submit" class="btn-success" style="width:100%;">✅ Ödemeyi Kaydet</button>
            </form>
        </div>
    </div>
</div>

<!-- Toplu SMS Modal -->
<div id="topluSmsModal" class="modal">
    <div class="modal-content" style="max-width:500px;">
        <div class="modal-header">
            <h2>📱 Toplu SMS Gönder</h2>
            <span class="close-btn" onclick="closeModal('topluSmsModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="topluSmsForm">
                <div class="form-group">
                    <label>Seçili Hasta Sayısı</label>
                    <input type="text" id="selectedPatientCount" disabled style="background:#f5f5f5;">
                </div>
                <div class="form-group">
                    <label>SMS İçeriği</label>
                    <textarea name="mesaj" rows="4" placeholder="SMS içeriğini yazın..." required></textarea>
                </div>
                <button type="submit" class="btn-warning">📱 SMS Gönder</button>
                <button type="button" onclick="closeModal('topluSmsModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

<!-- Toplu Email Modal -->
<div id="topluEmailModal" class="modal">
    <div class="modal-content" style="max-width:500px;">
        <div class="modal-header">
            <h2>📧 Toplu E-posta Gönder</h2>
            <span class="close-btn" onclick="closeModal('topluEmailModal')">&times;</span>
        </div>
        <div class="modal-body">
            <form id="topluEmailForm">
                <div class="form-group">
                    <label>Seçili Hasta Sayısı</label>
                    <input type="text" id="selectedEmailPatientCount" disabled style="background:#f5f5f5;">
                </div>
                <div class="form-group">
                    <label>Konu</label>
                    <input type="text" name="konu" placeholder="E-posta konusu..." required>
                </div>
                <div class="form-group">
                    <label>E-posta İçeriği</label>
                    <textarea name="icerik" rows="6" placeholder="E-posta içeriğini yazın..." required></textarea>
                </div>
                <button type="submit" class="btn-info">📧 E-posta Gönder</button>
                <button type="button" onclick="closeModal('topluEmailModal')" class="btn-danger">İptal</button>
            </form>
        </div>
    </div>
</div>

