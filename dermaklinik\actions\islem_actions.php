<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_session') {
        $patient_id = (int)$_POST['patient_id'];
        $tarih = $_POST['tarih'];
        $islem_tipi = trim($_POST['islem_tipi']);
        $bolge = trim($_POST['bolge']);
        $kullanilan_urun = trim($_POST['kullanilan_urun']);
        $ucret = (float)$_POST['ucret'];
        $uygulayan_personel = trim($_POST['uygulayan_personel']);
        $aciklama = trim($_POST['aciklama']);

        try {
            $stmt = $pdo->prepare("INSERT INTO patient_sessions (patient_id, tarih, islem_tipi, bolge, kullanilan_urun, ucret, uygulayan_personel, aciklama, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$patient_id, $tarih, $islem_tipi, $bolge, $kullanilan_urun, $ucret, $uygulayan_personel, $aciklama]);
            echo json_encode(['success' => true, 'message' => 'İşlem başarıyla eklendi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

