<?php
if (!isset($_GET['id'])) {
    redirect('?tab=hastalar');
}

$patient_id = (int)$_GET['id'];

$stmt = $pdo->prepare("SELECT * FROM patients WHERE id = ?");
$stmt->execute([$patient_id]);
$patient = $stmt->fetch();

if (!$patient) {
    echo "<div class='tab-content active'><h2>Hasta Bulunamadı</h2><p>Belirtilen ID'ye sahip hasta bulunamadı.</p><a href='?tab=hastalar' class='btn-primary'>Hastalar Listesine Dön</a></div>";
    exit;
}

// Genel İşlemler (Gruplu)
$stmt = $pdo->prepare("SELECT * FROM patient_sessions WHERE patient_id = ? ORDER BY tarih DESC");
$stmt->execute([$patient_id]);
$sessions = $stmt->fetchAll();

// <PERSON><PERSON> İşlemleri (Gruplu)
$stmt = $pdo->prepare("SELECT * FROM patient_laser_sessions WHERE patient_id = ? ORDER BY tarih DESC");
$stmt->execute([$patient_id]);
$lasers = $stmt->fetchAll();

// Tahsilatlar
$stmt = $pdo->prepare("SELECT * FROM collections WHERE patient_id = ? ORDER BY tarih DESC");
$stmt->execute([$patient_id]);
$collections = $stmt->fetchAll();
?>

<div class="tab-content active">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2>Hasta: <?= htmlspecialchars($patient['ad_soyad'] ?? 'Belirtilmemiş') ?></h2>
        <a href="?tab=hastalar" class="btn-primary">← Hastalar Listesine Dön</a>
    </div>
    <div class="info-grid">
        <div class="info-item"><span class="info-label">Ad Soyad:</span> <?= htmlspecialchars($patient['ad_soyad'] ?? 'Belirtilmemiş') ?></div>
        <div class="info-item"><span class="info-label">TC:</span> <?= htmlspecialchars($patient['tc_kimlik_no'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Telefon:</span> <?= htmlspecialchars($patient['telefon'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Doğum Tarihi:</span> <?= formatDate($patient['dogum_tarihi'] ?? null) ?></div>
        <div class="info-item"><span class="info-label">Cinsiyet:</span> <?= htmlspecialchars($patient['cinsiyet'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Email:</span> <?= htmlspecialchars($patient['email'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Adres:</span> <?= htmlspecialchars($patient['adres'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Deri Tipi:</span> <?= htmlspecialchars($patient['deri_tipi'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Ek Hastalıklar:</span> <?= htmlspecialchars($patient['ek_hastaliklar'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">İlaçlar:</span> <?= htmlspecialchars($patient['kullandigi_ilaclar'] ?? '-') ?></div>
        <div class="info-item"><span class="info-label">Notlar:</span> <?= htmlspecialchars($patient['notlar'] ?? '-') ?></div>
    </div>

    <!-- Genel İşlemler -->
    <h3 class="section-title">💉 Genel İşlemler</h3>
    <button class="btn-info" onclick="openModal('genelIslemModal', <?= $patient_id ?>)">+ Yeni İşlem Ekle</button>
    <div id="sessions-grouped">
        <?php
        $groupedSessions = [];
        foreach ($sessions as $s) {
            $groupedSessions[$s['tarih']][] = $s;
        }

        foreach ($groupedSessions as $date => $daySessions):
            $totalAmount = 0;
            $totalPaid = 0;

            foreach ($daySessions as $s) {
                $totalAmount += $s['ucret'];
                $stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE session_id = ?");
                $stmt->execute([$s['id']]);
                $paidRow = $stmt->fetch();
                $totalPaid += $paidRow['total'] ?? 0;
            }
        ?>
        <div class="date-group">
            <div class="date-group-header">
                <div class="date-group-title">📅 <?= formatDate($date) ?></div>
                <div class="date-group-summary">
                    <strong><?= count($daySessions) ?> işlem</strong> | 
                    Toplam: <strong><?= number_format($totalAmount, 2, ',', '.') ?> TL</strong> | 
                    Ödenen: <strong><?= number_format($totalPaid, 2, ',', '.') ?> TL</strong> | 
                    Kalan: <strong class="<?= ($totalAmount - $totalPaid > 0) ? 'badge-warning' : 'badge-success' ?>">
                        <?= number_format($totalAmount - $totalPaid, 2, ',', '.') ?> TL</strong>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>İşlem</th>
                        <th>Bölge</th>
                        <th>Ürün</th>
                        <th>Tutar</th>
                        <th>Ödenen</th>
                        <th>Kalan</th>
                        <th>Personel</th>
                        <th>İşlem</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($daySessions as $s):
                        $stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE session_id = ?");
                        $stmt->execute([$s['id']]);
                        $paidRow = $stmt->fetch();
                        $paid = $paidRow['total'] ?? 0;
                        $remaining = $s['ucret'] - $paid;
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($s['islem_tipi']) ?></td>
                        <td><?= htmlspecialchars($s['bolge'] ?? '-') ?></td>
                        <td><?= htmlspecialchars($s['kullanilan_urun'] ?? '-') ?></td>
                        <td><strong><?= number_format($s['ucret'], 2, ',', '.') ?> TL</strong></td>
                        <td><?= number_format($paid, 2, ',', '.') ?> TL</td>
                        <td><strong class="<?= $remaining > 0 ? 'badge-warning' : 'badge-success' ?>"><?= number_format($remaining, 2, ',', '.') ?> TL</strong></td>
                        <td><?= htmlspecialchars($s['uygulayan_personel'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown">
                                <button class="dropdown-btn" onclick="toggleDropdown(this)">⋮</button>
                                <div class="dropdown-content">
                                    <a href="#" onclick="Swal.fire({icon: 'info', title: 'Bilgi', text: 'İncele - ID: <?= $s['id'] ?>', confirmButtonText: 'Tamam'})">İncele</a>
                                    <a href="#" onclick="openEditSessionModal(<?= $s['id'] ?>)">Düzenle</a>
                                    <a href="#" onclick="deleteSession(<?= $s['id'] ?>)" class="text-danger">Sil</a>
                                    <?php if ($remaining > 0): ?>
                                    <a href="#" onclick="openPaymentModal(<?= $s['id'] ?>, 'genel', '<?= addslashes($s['islem_tipi']) ?>', '<?= $s['tarih'] ?>', <?= $s['ucret'] ?>, <?= $paid ?>, <?= $patient_id ?>)">Öde</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endforeach; ?>
        
        <?php if (empty($sessions)): ?>
            <p style="text-align:center; color:#7f8c8d; padding:20px;">Henüz genel işlem girilmemiş.</p>
        <?php endif; ?>
    </div>

    <!-- Lazer İşlemleri -->
    <h3 class="section-title laser">🔬 Lazer İşlemleri</h3>
    <button class="btn-info" onclick="openModal('lazerIslemModal', <?= $patient_id ?>)" style="background:#9b59b6;">+ Yeni Lazer İşlemi</button>
    <div id="laser-grouped">
        <?php
        $groupedLasers = [];
        foreach ($lasers as $l) {
            $groupedLasers[$l['tarih']][] = $l;
        }

        foreach ($groupedLasers as $date => $dayLasers):
            $totalAmount = 0;
            $totalPaid = 0;

            foreach ($dayLasers as $l) {
                $totalAmount += $l['ucret'];
                $stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE laser_session_id = ?");
                $stmt->execute([$l['id']]);
                $paidRow = $stmt->fetch();
                $totalPaid += $paidRow['total'] ?? 0;
            }
        ?>
        <div class="date-group">
            <div class="date-group-header">
                <div class="date-group-title">📅 <?= formatDate($date) ?></div>
                <div class="date-group-summary">
                    <strong><?= count($dayLasers) ?> işlem</strong> | 
                    Toplam: <strong><?= number_format($totalAmount, 2, ',', '.') ?> TL</strong> | 
                    Ödenen: <strong><?= number_format($totalPaid, 2, ',', '.') ?> TL</strong> | 
                    Kalan: <strong class="<?= ($totalAmount - $totalPaid > 0) ? 'badge-warning' : 'badge-success' ?>">
                        <?= number_format($totalAmount - $totalPaid, 2, ',', '.') ?> TL</strong>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Bölge</th>
                        <th>Cihaz</th>
                        <th>Joule</th>
                        <th>Ücret</th>
                        <th>Ödenen</th>
                        <th>Kalan</th>
                        <th>Personel</th>
                        <th>İşlem</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dayLasers as $l):
                        $stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE laser_session_id = ?");
                        $stmt->execute([$l['id']]);
                        $paidRow = $stmt->fetch();
                        $paid = $paidRow['total'] ?? 0;
                        $remaining = $l['ucret'] - $paid;
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($l['bolge']) ?></td>
                        <td><?= htmlspecialchars($l['cihaz'] ?? '-') ?></td>
                        <td><?= htmlspecialchars($l['joule'] ?? '-') ?></td>
                        <td><strong><?= number_format($l['ucret'], 2, ',', '.') ?> TL</strong></td>
                        <td><?= number_format($paid, 2, ',', '.') ?> TL</td>
                        <td><strong class="<?= $remaining > 0 ? 'badge-warning' : 'badge-success' ?>"><?= number_format($remaining, 2, ',', '.') ?> TL</strong></td>
                        <td><?= htmlspecialchars($l['uygulayan_personel'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown">
                                <button class="dropdown-btn" onclick="toggleDropdown(this)">⋮</button>
                                <div class="dropdown-content">
                                    <a href="#" onclick="Swal.fire({icon: 'info', title: 'Bilgi', text: 'İncele - ID: <?= $l['id'] ?>', confirmButtonText: 'Tamam'})">İncele</a>
                                    <a href="#" onclick="openEditLaserModal(<?= $l['id'] ?>)">Düzenle</a>
                                    <a href="#" onclick="deleteLaserSession(<?= $l['id'] ?>)" class="text-danger">Sil</a>
                                    <?php if ($remaining > 0): ?>
                                    <a href="#" onclick="openPaymentModal(<?= $l['id'] ?>, 'lazer', 'Lazer - <?= addslashes($l['bolge']) ?>', '<?= $l['tarih'] ?>', <?= $l['ucret'] ?>, <?= $paid ?>, <?= $patient_id ?>)">Öde</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endforeach; ?>
        
        <?php if (empty($lasers)): ?>
            <p style="text-align:center; color:#7f8c8d; padding:20px;">Henüz lazer işlemi girilmemiş.</p>
        <?php endif; ?>
    </div>

    <!-- Tahsilatlar -->
    <h3 class="section-title payments">💰 Tahsilat Geçmişi</h3>
    <div style="background:#fffaf0; padding:15px; border-radius:10px; border:1px solid #fadfad;">
        <table>
            <thead>
                <tr style="background:#e67e22; color:white;">
                    <th>Tarih</th>
                    <th>Tutar</th>
                    <th>Ödeme Şekli</th>
                    <th>Açıklama</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($collections)): ?>
                    <tr><td colspan="4" style="text-align:center; padding:20px;">Henüz tahsilat yapılmamış.</td></tr>
                <?php else: ?>
                    <?php foreach ($collections as $collection): ?>
                    <tr>
                        <td><?= formatDate($collection['tarih']) ?></td>
                        <td><strong><?= number_format($collection['tutar'], 2, ',', '.') ?> TL</strong></td>
                        <td><?= htmlspecialchars($collection['odeme_sekli']) ?></td>
                        <td><?= htmlspecialchars($collection['aciklama']) ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
