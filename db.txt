-- <PERSON>eritabanı Oluşturma
CREATE DATABASE IF NOT EXISTS dermaklinik CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE dermaklinik;

-- Hastalar
CREATE TABLE patients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_soyad VARCHAR(255) NOT NULL,
    tc_kimlik_no VARCHAR(11) UNIQUE NOT NULL,
    dogum_tarihi DATE,
    cinsiyet ENUM('<PERSON>rk<PERSON>','Kadın') NOT NULL,
    telefon VARCHAR(20),
    email VARCHAR(255),
    adres TEXT,
    deri_tipi VARCHAR(100),
    ek_hastaliklar TEXT,
    kullandigi_ilaclar TEXT,
    notlar TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON>
CREATE TABLE patient_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    tarih DATE NOT NULL,
    islem_tipi VARCHAR(255) NOT NULL,
    bolge VARCHAR(255),
    kullanilan_urun VARCHAR(255),
    aciklama TEXT,
    ucret DECIMAL(10,2) DEFAULT 0.00,
    uygulayan_personel VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Lazer İşlemleri
CREATE TABLE patient_laser_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    tarih DATE NOT NULL,
    bolge VARCHAR(255),
    cihaz VARCHAR(255),
    joule VARCHAR(50),
    percent INT,
    spot VARCHAR(50),
    time VARCHAR(50),
    baslik VARCHAR(255),
    uygulayan_personel VARCHAR(255),
    ucret DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Tahsilatlar
CREATE TABLE collections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    session_id INT NULL,
    laser_session_id INT NULL,
    tarih DATE NOT NULL,
    tutar DECIMAL(10,2) NOT NULL,
    odeme_sekli ENUM('Nakit','Kredi Kartı','Havale','Taksit') NOT NULL,
    aciklama TEXT,
    kasaya_islendi_mi BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES patient_sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (laser_session_id) REFERENCES patient_laser_sessions(id) ON DELETE SET NULL
);

-- Randevular
CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    tarih DATE NOT NULL,
    saat TIME NOT NULL,
    islem_tipi VARCHAR(255),
    bolge VARCHAR(255),
    durum ENUM('Bekliyor','Tamamlandı','İptal') DEFAULT 'Bekliyor',
    notlar TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Tanımlamalar
CREATE TABLE definitions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('session_type','area','product','device') NOT NULL,
    value VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_def (type, value)
);

-- Kullanıcılar (Basit)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_soyad VARCHAR(255) NOT NULL,
    kullanici_adi VARCHAR(100) UNIQUE NOT NULL,
    sifre VARCHAR(255) NOT NULL,
    rol ENUM('admin','doktor','personel','sekreter') DEFAULT 'personel',
    telefon VARCHAR(20),
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Örnek Kullanıcı
INSERT INTO users (ad_soyad, kullanici_adi, sifre, rol) VALUES 
('Dr. Ayşe Yılmaz', 'drayse', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'doktor');

-- Örnek Tanımlar
INSERT INTO definitions (type, value) VALUES
('session_type', 'Aquapell'),
('session_type', 'PRP'),
('session_type', 'Mezoterapi'),
('session_type', 'Botoks'),
('area', 'Yüz'),
('area', 'Boyun'),
('area', 'El'),
('area', 'Bacak'),
('area', 'Kol'),
('area', 'SM+SC+S3'),
('product', 'Serum X'),
('product', 'Hyaluronik Asit'),
('product', 'Vitamin C'),
('product', 'Retinol'),
('device', 'GentleLase'),
('device', 'Alexandrite'),
('device', 'Diode Laser'),
('device', 'Nd:YAG');