<?php
require_once '../includes/db.php';
require_once '../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_payment') {
        $session_id = (int)$_POST['session_id'];
        $session_type = $_POST['session_type'];
        $patient_id = (int)$_POST['patient_id'];
        $tutar = (float)$_POST['tutar'];
        $odeme_sekli = $_POST['odeme_sekli'];
        $aciklama = trim($_POST['aciklama']);

        try {
            $stmt = $pdo->prepare("INSERT INTO collections (patient_id, session_id, laser_session_id, tarih, tutar, odeme_sekli, aciklama, kasaya_islendi_mi, created_at) VALUES (?, ?, ?, CURDATE(), ?, ?, ?, 1, NOW())");
            
            if ($session_type === 'genel') {
                $stmt->execute([$patient_id, $session_id, null, $tutar, $odeme_sekli, $aciklama]);
            } else {
                $stmt->execute([$patient_id, null, $session_id, $tutar, $odeme_sekli, $aciklama]);
            }
            
            echo json_encode(['success' => true, 'message' => 'Ödeme başarıyla kaydedildi.']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Hata: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

