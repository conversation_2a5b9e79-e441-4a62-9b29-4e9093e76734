* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* SIDEBAR STYLES */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 250px;
    height: 100vh;
    background: #2c3e50;
    color: white;
    padding: 20px 0;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    transform: translateX(-250px);
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid #34495e;
    margin-bottom: 20px;
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 12px 20px;
    transition: background 0.3s;
    border-radius: 0 25px 25px 0;
    margin-right: 10px;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: #34495e;
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
}

/* MAIN CONTENT */
.main-content {
    margin-left: 250px;
    transition: margin-left 0.3s ease;
    min-height: 100vh;
}

.main-content.expanded {
    margin-left: 0;
}

/* HEADER */
.header {
    background: white;
    padding: 15px 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    margin-right: 15px;
    color: #2c3e50;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* TAB CONTENT */
.tab-content {
    display: none;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-top: 20px;
}

.tab-content.active {
    display: block;
}

/* TABLES */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 0.95rem;
    background: white;
    border-radius: 8px;
    overflow: visible;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
    position: relative;
    overflow: visible;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

/* BUTTONS */
button {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    margin-right: 8px;
    font-weight: 500;
    transition: background 0.2s;
    font-size: 0.9rem;
}

button:hover {
    background: #2980b9;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

.btn-warning {
    background: #f39c12;
}

.btn-warning:hover {
    background: #d68910;
}

.btn-info {
    background: #17a2b8;
}

.btn-info:hover {
    background: #138496;
}

.btn-primary {
    background: #007bff;
}

.btn-primary:hover {
    background: #0056b3;
}

/* FORM STYLES */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input, 
.form-group select, 
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.95rem;
}

.form-group input:focus, 
.form-group select:focus, 
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
}

/* MODAL STYLES */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow: auto;
    padding: 20px 0;
}

.modal-content {
    background: white;
    margin: 30px auto;
    max-width: 95%;
    max-width: 1000px;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    text-align: center;
    position: relative;
}

.modal-header h2 {
    margin: 0;
    font-weight: 700;
    font-size: 1.8rem;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    font-size: 28px;
    font-weight: bold;
    color: white;
}

.modal-body {
    padding: 30px;
}

/* INFO GRID */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    font-size: 0.95rem;
    line-height: 1.6;
    background: #f8f9ff;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #eef2ff;
    margin-bottom: 30px;
}

.info-item {
    margin-bottom: 5px;
}

.info-label {
    font-weight: 600;
    color: #2c3e50;
}

/* SECTION TITLES */
.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin: 20px 0 15px;
    padding-left: 12px;
    border-left: 4px solid #3498db;
    display: flex;
    align-items: center;
}

.section-title.laser {
    border-left-color: #9b59b6;
}

.section-title.payments {
    border-left-color: #e67e22;
}

/* DATE GROUPS */
.date-group {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.02);
    overflow: visible;
}

.date-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.date-group-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #1e293b;
}

.date-group-summary {
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* BADGES */
.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-warning {
    background: #f39c12;
    color: white;
}

.badge-success {
    background: #27ae60;
    color: white;
}

.badge-danger {
    background: #e74c3c;
    color: white;
}

.paid-badge {
    background: #27ae60;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

/* PAYMENT BUTTON */
.payment-btn {
    background: #e67e22;
    padding: 4px 10px;
    font-size: 0.85rem;
}

.payment-btn:hover {
    background: #d35400;
}

/* DROPDOWN */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-btn {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    transition: background-color 0.2s;
}

.dropdown-btn:hover {
    background: #5a6268;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 120px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 9999;
    border-radius: 4px;
    overflow: hidden;
    right: 0;
    top: 100%;
}

.dropdown-content a {
    color: black;
    padding: 8px 12px;
    text-decoration: none;
    display: block;
    font-size: 0.85rem;
    cursor: pointer;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.dropdown.active .dropdown-content {
    display: block;
}

/* BULK ACTIONS */
.bulk-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.bulk-actions input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.bulk-actions label {
    margin: 0;
    font-weight: 500;
}

/* DASHBOARD CARDS */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dashboard-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid;
}

.dashboard-card.primary {
    border-left-color: #3498db;
}

.dashboard-card.success {
    border-left-color: #2ecc71;
}

.dashboard-card.warning {
    border-left-color: #e67e22;
}

.dashboard-card h4 {
    margin-bottom: 10px;
    color: #7f8c8d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.dashboard-card h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

/* UTILITY CLASSES */
.text-danger {
    color: #e74c3c !important;
}

.text-success {
    color: #27ae60 !important;
}

.text-warning {
    color: #f39c12 !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

/* RESPONSIVE */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-250px);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .main-content.expanded {
        margin-left: 0;
    }
    
    th, td {
        padding: 8px 4px;
        font-size: 14px;
    }
    
    .modal-content {
        margin: 10px;
        max-width: 95%;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-header {
        padding: 20px;
    }
    
    .modal-header h2 {
        font-size: 1.5rem;
    }
}
