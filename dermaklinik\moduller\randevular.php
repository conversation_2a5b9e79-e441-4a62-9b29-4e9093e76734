<?php
$stmt = $pdo->prepare("
    SELECT a.*, p.ad_soyad 
    FROM appointments a 
    LEFT JOIN patients p ON a.patient_id = p.id 
    ORDER BY a.tarih DESC, a.saat DESC
");
$stmt->execute();
$appointments = $stmt->fetchAll();
?>

<h2>Randevular</h2>
<div class="form-group" style="max-width: 400px;">
    <input type="text" id="search-appointment" placeholder="Hasta adı veya işlem ile ara..." oninput="filterAppointments()" style="padding: 12px; font-size: 1rem;">
</div>
<button class="btn-primary" onclick="openModal('randevuModal')">+ <PERSON><PERSON></button>

<table>
    <thead>
        <tr>
            <th>Hasta</th>
            <th>Tarih</th>
            <th>Saat</th>
            <th>İşlem</th>
            <th>Bölge</th>
            <th>Durum</th>
            <th><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($appointments as $appointment): ?>
        <tr>
            <td><?= htmlspecialchars($appointment['ad_soyad']) ?></td>
            <td><?= formatDate($appointment['tarih']) ?></td>
            <td><?= $appointment['saat'] ?></td>
            <td><?= htmlspecialchars($appointment['islem_tipi']) ?></td>
            <td><?= htmlspecialchars($appointment['bolge'] ?? '-') ?></td>
            <td>
                <span class="badge <?= $appointment['durum'] === 'Tamamlandı' ? 'badge-success' : ($appointment['durum'] === 'İptal' ? 'badge-danger' : 'badge-warning') ?>">
                    <?= $appointment['durum'] ?>
                </span>
            </td>
            <td>
                <div class="dropdown">
                    <button class="dropdown-btn" onclick="toggleDropdown(this)">⋮</button>
                    <div class="dropdown-content">
                        <a href="#" onclick="editAppointment(<?= $appointment['id'] ?>)">Düzenle</a>
                        <a href="#" onclick="deleteAppointment(<?= $appointment['id'] ?>)" class="text-danger">Sil</a>
                    </div>
                </div>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<script>
function filterAppointments() {
    const term = document.getElementById('search-appointment').value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(term) ? '' : 'none';
    });
}

function editAppointment(id) {
    Swal.fire({
        icon: 'info',
        title: 'Bilgi',
        text: 'Randevu düzenleme özelliği yakında eklenecek. ID: ' + id,
        confirmButtonText: 'Tamam'
    });
}

function deleteAppointment(id) {
    Swal.fire({
        title: 'Emin misiniz?',
        text: 'Bu randevuyu silmek istediğinizden emin misiniz?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Evet, Sil!',
        cancelButtonText: 'İptal'
    }).then((result) => {
        if (result.isConfirmed) {
    
            fetch('actions/randevu_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=delete_appointment&id=' + id
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata!',
                        text: data.message,
                        confirmButtonText: 'Tamam'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Hata!',
                    text: 'Bir hata oluştu!',
                    confirmButtonText: 'Tamam'
                });
            });
        }
    });
}
</script>
