-- Definitions tablosunu oluştur
CREATE TABLE IF NOT EXISTS definitions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    value VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> veriler ekle
INSERT INTO definitions (type, value) VALUES
('islem_tipi', 'Aquapell'),
('islem_tipi', 'PRP'),
('islem_tipi', 'Botox'),
('bolge', 'Yüz'),
('bolge', 'Boyun'),
('bolge', 'Dekolte'),
('urun', 'Hyaluronik Asit'),
('urun', 'Vitamin C'),
('personel', 'Dr. Ahmet Yılmaz'),
('personel', 'Hemş<PERSON> Ayşe Demir'),
('cihaz', 'GentleLase'),
('cihaz', 'Nd:YAG'),
('odeme_sekli', 'Nakit'),
('odeme_sekli', '<PERSON><PERSON><PERSON>'),
('odeme_sekli', 'Ha<PERSON>');

