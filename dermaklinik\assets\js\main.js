// GLOBAL STATE
let currentPatientId = null;
let selectedPatients = [];

// INIT
document.addEventListener('DOMContentLoaded', function() {
    // Bugünün tarihini form alanlarına set et
    const today = new Date().toISOString().split('T')[0];
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });
    
    // Form submit eventlerini dinle
    setupFormHandlers();
});

// FORM HANDLERS
function setupFormHandlers() {
    // Hasta ekle formu
    const hastaEkleForm = document.getElementById('hastaEkleForm');
    if (hastaEkleForm) {
        hastaEkleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            savePatient();
        });
    }

    // Genel işlem formu
    const genelIslemForm = document.getElementById('genelIslemForm');
    if (genelIslemForm) {
        genelIslemForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveSession();
        });
    }

    // Lazer işlem formu
    const lazerIslemForm = document.getElementById('lazerIslemForm');
    if (lazerIslemForm) {
        lazerIslemForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveLaserSession();
        });
    }

    // Randevu formu
    const randevuForm = document.getElementById('randevuForm');
    if (randevuForm) {
        randevuForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveAppointment();
        });
    }

    // Ödeme formu
    const odemeForm = document.getElementById('odemeForm');
    if (odemeForm) {
        odemeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            savePayment();
        });
    }

    // Toplu SMS formu
    const topluSmsForm = document.getElementById('topluSmsForm');
    if (topluSmsForm) {
        topluSmsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendBulkSms();
        });
    }

    // Toplu Email formu
    const topluEmailForm = document.getElementById('topluEmailForm');
    if (topluEmailForm) {
        topluEmailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendBulkEmail();
        });
    }
}

// SIDEBAR TOGGLE
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.toggle('show');
    } else {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    }
}

// MODAL CONTROLS
function openModal(modalId, patientId = null) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        
        // Eğer hasta ID'si verilmişse, form alanlarına set et
        if (patientId && modalId === 'genelIslemModal') {
            document.getElementById('genelIslemPatientId').value = patientId;
        }
        if (patientId && modalId === 'lazerIslemModal') {
            document.getElementById('lazerIslemPatientId').value = patientId;
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        // Formu temizle
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// Modal dışına tıklandığında kapat
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}

// HASTA İŞLEMLERİ
function savePatient() {
    const form = document.getElementById('hastaEkleForm');
    const formData = new FormData(form);
    formData.append('action', 'add_patient');

    fetch('actions/hasta_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('hastaEkleModal');
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// GENEL İŞLEM KAYDET
function saveSession() {
    const form = document.getElementById('genelIslemForm');
    const formData = new FormData(form);
    formData.append('action', 'add_session');

    fetch('actions/islem_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('genelIslemModal');
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// LAZER İŞLEM KAYDET
function saveLaserSession() {
    const form = document.getElementById('lazerIslemForm');
    const formData = new FormData(form);
    formData.append('action', 'add_laser_session');

    fetch('actions/lazer_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('lazerIslemModal');
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// RANDEVU KAYDET
function saveAppointment() {
    const form = document.getElementById('randevuForm');
    const formData = new FormData(form);
    formData.append('action', 'add_appointment');

    fetch('actions/randevu_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('randevuModal');
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// ÖDEME MODALI AÇ
function openPaymentModal(sessionId, type, description, date, total, paid, patientId) {
    document.getElementById('odemeSessionId').value = sessionId;
    document.getElementById('odemeSessionType').value = type;
    document.getElementById('odemePatientId').value = patientId;
    document.getElementById('odemeSessionDate').value = formatDate(date);
    document.getElementById('odemeSessionTypeDisplay').value = description;
    document.getElementById('odemeTotal').value = total;
    document.getElementById('odemePaid').value = paid;
    document.getElementById('odemeAmount').value = (total - paid).toFixed(2);
    
    // Açıklama alanını otomatik doldur
    const aciklamaField = document.querySelector('#odemeModal input[name="aciklama"]');
    if (aciklamaField) {
        aciklamaField.value = description + ' - Ödeme';
    }
    
    openModal('odemeModal');
}

// ÖDEMEYİ KAYDET
function savePayment() {
    const form = document.getElementById('odemeForm');
    const formData = new FormData(form);
    formData.append('action', 'add_payment');

    fetch('actions/odeme_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('odemeModal');
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// TOPLU İŞLEMLER
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all');
    const patientCheckboxes = document.querySelectorAll('.patient-checkbox');
    
    patientCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedPatients();
}

function updateSelectedPatients() {
    const patientCheckboxes = document.querySelectorAll('.patient-checkbox:checked');
    selectedPatients = Array.from(patientCheckboxes).map(cb => cb.value);
    
    // Header checkbox'ı güncelle
    const selectAllCheckbox = document.getElementById('select-all');
    const allCheckboxes = document.querySelectorAll('.patient-checkbox');
    selectAllCheckbox.checked = patientCheckboxes.length === allCheckboxes.length;
}

// Hasta checkbox'ları için event listener
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('patient-checkbox')) {
        updateSelectedPatients();
    }
});

// TOPLU SMS MODAL
function openBulkSmsModal() {
    if (selectedPatients.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'Uyarı!',
            text: 'Lütfen önce hastaları seçin!',
            confirmButtonText: 'Tamam'
        });
        return;
    }
    
    document.getElementById('selectedPatientCount').value = selectedPatients.length + ' hasta seçildi';
    openModal('topluSmsModal');
}

// TOPLU EMAIL MODAL
function openBulkEmailModal() {
    if (selectedPatients.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'Uyarı!',
            text: 'Lütfen önce hastaları seçin!',
            confirmButtonText: 'Tamam'
        });
        return;
    }
    
    document.getElementById('selectedEmailPatientCount').value = selectedPatients.length + ' hasta seçildi';
    openModal('topluEmailModal');
}

// TOPLU SMS GÖNDER
function sendBulkSms() {
    const form = document.getElementById('topluSmsForm');
    const formData = new FormData(form);
    formData.append('action', 'send_bulk_sms');
    formData.append('patient_ids', JSON.stringify(selectedPatients));

    fetch('actions/toplu_iletisim_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('topluSmsModal');
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// TOPLU EMAIL GÖNDER
function sendBulkEmail() {
    const form = document.getElementById('topluEmailForm');
    const formData = new FormData(form);
    formData.append('action', 'send_bulk_email');
    formData.append('patient_ids', JSON.stringify(selectedPatients));

    fetch('actions/toplu_iletisim_actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Başarılı!',
                text: data.message,
                confirmButtonText: 'Tamam'
            }).then(() => {
                closeModal('topluEmailModal');
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Hata!',
                text: data.message,
                confirmButtonText: 'Tamam'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Hata!',
            text: 'Bir hata oluştu!',
            confirmButtonText: 'Tamam'
        });
    });
}

// HASTA ARAMA
function filterPatients() {
    const term = document.getElementById('search-patient').value.toLowerCase();
    const rows = document.querySelectorAll('.selectable-row');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(term) ? '' : 'none';
    });
}

// YARDIMCI FONKSİYONLAR
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
}

// DROPDOWN MENU
function toggleDropdown(element) {
    // Önce tüm dropdown'ları kapat
    document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.classList.remove('active');
    });
    
    // Tıklanan dropdown'ı aç
    const dropdown = element.closest('.dropdown');
    dropdown.classList.toggle('active');
}

// Dropdown dışına tıklandığında kapat
document.addEventListener('click', function(e) {
    if (!e.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    }
});

// Dropdown menü linklerine tıklandığında menüyü kapat
document.addEventListener('click', function(e) {
    if (e.target.closest('.dropdown-content a')) {
        const dropdown = e.target.closest('.dropdown');
        if (dropdown) {
            dropdown.classList.remove('active');
        }
    }
});

// RESPONSIVE MENU
function handleResize() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
    }
}

window.addEventListener('resize', handleResize);
handleResize(); // İlk yüklemede çalıştır
