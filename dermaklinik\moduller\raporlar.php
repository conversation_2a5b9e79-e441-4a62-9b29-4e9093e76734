<?php
// Rapor verilerini hazırla
$today = date('Y-m-d');
$month_start = date('Y-m-01');
$year_start = date('Y-01-01');

// Günlük gelir
$stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE tarih = ?");
$stmt->execute([$today]);
$daily_income = $stmt->fetch()['total'] ?? 0;

// Aylık gelir
$stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE tarih >= ?");
$stmt->execute([$month_start]);
$monthly_income = $stmt->fetch()['total'] ?? 0;

// Yıllık gelir
$stmt = $pdo->prepare("SELECT SUM(tutar) as total FROM collections WHERE tarih >= ?");
$stmt->execute([$year_start]);
$yearly_income = $stmt->fetch()['total'] ?? 0;

// Toplam hasta sayısı
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM patients");
$stmt->execute();
$total_patients = $stmt->fetch()['count'];

// Toplam işlem sayısı
$stmt = $pdo->prepare("
    SELECT 
        (SELECT COUNT(*) FROM patient_sessions) + 
        (SELECT COUNT(*) FROM patient_laser_sessions) as total_sessions
");
$stmt->execute();
$total_sessions = $stmt->fetch()['total_sessions'] ?? 0;
?>

<h2>Raporlar</h2>

<div class="dashboard-cards">
    <div class="dashboard-card primary">
        <h4>Günlük Gelir</h4>
        <h2><?= number_format($daily_income, 2, ',', '.') ?> TL</h2>
    </div>
    <div class="dashboard-card success">
        <h4>Aylık Gelir</h4>
        <h2><?= number_format($monthly_income, 2, ',', '.') ?> TL</h2>
    </div>
    <div class="dashboard-card warning">
        <h4>Yıllık Gelir</h4>
        <h2><?= number_format($yearly_income, 2, ',', '.') ?> TL</h2>
    </div>
    <div class="dashboard-card" style="border-left-color: #17a2b8;">
        <h4>Toplam İşlem</h4>
        <h2><?= $total_sessions ?></h2>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
    <!-- Gelir Raporu -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">💰 Gelir Raporu</h3>
        <?php
        $stmt = $pdo->prepare("
            SELECT odeme_sekli, SUM(tutar) as total 
            FROM collections 
            GROUP BY odeme_sekli 
            ORDER BY total DESC
        ");
        $stmt->execute();
        $income_by_method = $stmt->fetchAll();
        ?>
        <?php if (empty($income_by_method)): ?>
            <p style="color: #7f8c8d; text-align: center; padding: 20px;">Henüz gelir kaydı yok.</p>
        <?php else: ?>
            <table style="font-size: 0.9rem;">
                <thead>
                    <tr>
                        <th>Ödeme Şekli</th>
                        <th>Toplam</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($income_by_method as $method): ?>
                    <tr>
                        <td><?= htmlspecialchars($method['odeme_sekli']) ?></td>
                        <td><strong><?= number_format($method['total'], 2, ',', '.') ?> TL</strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <!-- Hasta İşlem Raporu -->
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
        <h3 style="margin-bottom: 15px; color: #2c3e50;">👥 Hasta İşlem Raporu</h3>
        <?php
        $stmt = $pdo->prepare("
            SELECT 
                p.ad_soyad,
                COUNT(DISTINCT ps.id) as genel_islem,
                COUNT(DISTINCT pls.id) as lazer_islem,
                COUNT(DISTINCT ps.id) + COUNT(DISTINCT pls.id) as toplam_islem
            FROM patients p
            LEFT JOIN patient_sessions ps ON p.id = ps.patient_id
            LEFT JOIN patient_laser_sessions pls ON p.id = pls.patient_id
            GROUP BY p.id, p.ad_soyad
            HAVING toplam_islem > 0
            ORDER BY toplam_islem DESC
            LIMIT 10
        ");
        $stmt->execute();
        $patient_reports = $stmt->fetchAll();
        ?>
        <?php if (empty($patient_reports)): ?>
            <p style="color: #7f8c8d; text-align: center; padding: 20px;">Henüz işlem kaydı yok.</p>
        <?php else: ?>
            <table style="font-size: 0.9rem;">
                <thead>
                    <tr>
                        <th>Hasta</th>
                        <th>Genel</th>
                        <th>Lazer</th>
                        <th>Toplam</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($patient_reports as $report): ?>
                    <tr>
                        <td><?= htmlspecialchars($report['ad_soyad']) ?></td>
                        <td><?= $report['genel_islem'] ?></td>
                        <td><?= $report['lazer_islem'] ?></td>
                        <td><strong><?= $report['toplam_islem'] ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Borç Raporu -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); margin-top: 20px;">
    <h3 style="margin-bottom: 15px; color: #2c3e50;">💸 Borç Raporu</h3>
    <?php
    $stmt = $pdo->prepare("
        SELECT 
            p.ad_soyad,
            COALESCE(SUM(ps.ucret), 0) + COALESCE(SUM(pls.ucret), 0) as toplam_tutar,
            COALESCE(SUM(c.tutar), 0) as odenen_tutar,
            (COALESCE(SUM(ps.ucret), 0) + COALESCE(SUM(pls.ucret), 0) - COALESCE(SUM(c.tutar), 0)) as kalan_borc
        FROM patients p
        LEFT JOIN patient_sessions ps ON p.id = ps.patient_id
        LEFT JOIN patient_laser_sessions pls ON p.id = pls.patient_id
        LEFT JOIN collections c ON (c.session_id = ps.id OR c.laser_session_id = pls.id)
        GROUP BY p.id, p.ad_soyad
        HAVING kalan_borc > 0
        ORDER BY kalan_borc DESC
    ");
    $stmt->execute();
    $debt_reports = $stmt->fetchAll();
    ?>
    <?php if (empty($debt_reports)): ?>
        <p style="color: #7f8c8d; text-align: center; padding: 20px;">Borçlu hasta yok.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>Hasta</th>
                    <th>Toplam Tutar</th>
                    <th>Ödenen</th>
                    <th>Kalan Borç</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($debt_reports as $debt): ?>
                <tr>
                    <td><strong><?= htmlspecialchars($debt['ad_soyad']) ?></strong></td>
                    <td><?= number_format($debt['toplam_tutar'], 2, ',', '.') ?> TL</td>
                    <td><?= number_format($debt['odenen_tutar'], 2, ',', '.') ?> TL</td>
                    <td><strong style="color: #e74c3c;"><?= number_format($debt['kalan_borc'], 2, ',', '.') ?> TL</strong></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>

